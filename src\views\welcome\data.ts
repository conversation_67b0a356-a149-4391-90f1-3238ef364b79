import { dayjs, cloneDeep, getRandomIntBetween } from "./utils";
import RiInbox2Fill from '@iconify-icons/ri/inbox-2-fill';
import RiShoppingBagFill from '@iconify-icons/ri/shopping-bag-fill';
import RiCalendarTodoFill from '@iconify-icons/ri/calendar-todo-fill';
import RiFileCloseFill from '@iconify-icons/ri/file-close-fill';



let chartData = []

/** 入库邮件、理货总包、出库序列号、异常邮件 */
chartData = [
  {
    icon: RiInbox2Fill,
    bgColor: "#effaff",
    color: "#41b6ff",
    duration: 2200,
    name: "入库邮件",
    value: 0,
    percent: "+88%",
    data: [20, 10000, 4239, 4962, 6752, 5208, 7450] // 平滑折线图数据
  },
  {
    icon: RiShoppingBagFill,
    bgColor: "#fff5f4",
    color: "#e85f33",
    duration: 1600,
    name: "理货总包",
    value: 0,
    percent: "+70%",
    data: [2216, 1148, 1255, 788, 4821, 1973, 4379]
  },
  {
    icon: RiCalendarTodoFill,
    bgColor: "#eff8f4",
    color: "#26ce83",
    duration: 1500,
    name: "出库序列号",
    value: 0,
    percent: "+99%",
    data: [861, 1002, 3195, 1715, 3666, 2415, 3645]
  },
  {
    icon: RiFileCloseFill,
    bgColor: "#f6f4fe",
    color: "#7846e5",
    duration: 100,
    name: "异常邮件",
    value: 0,
    percent: "+100%",
    data: [100]
  }
];


/** 分析概览 */
const barChartData = [
  {
    time: [],
    mailData: [],
    bagData: [],
  }
];

/** 解决概率 */
const progressData = [
  {
    week: "周一",
    percentage: 85,
    duration: 110,
    color: "#41b6ff"
  },
  {
    week: "周二",
    percentage: 86,
    duration: 105,
    color: "#41b6ff"
  },
  {
    week: "周三",
    percentage: 88,
    duration: 100,
    color: "#41b6ff"
  },
  {
    week: "周四",
    percentage: 89,
    duration: 95,
    color: "#41b6ff"
  },
  {
    week: "周五",
    percentage: 94,
    duration: 90,
    color: "#26ce83"
  },
  {
    week: "周六",
    percentage: 96,
    duration: 85,
    color: "#26ce83"
  },
  {
    week: "周日",
    percentage: 100,
    duration: 80,
    color: "#26ce83"
  }
].reverse();

/** 数据统计 */
// const tableData = Array.from({ length: 30 }).map((_, index) => {
//   return {
//     id: index + 1,
//     requiredNumber: getRandomIntBetween(13500, 19999),
//     questionNumber: getRandomIntBetween(12600, 16999),
//     resolveNumber: getRandomIntBetween(13500, 17999),
//     satisfaction: getRandomIntBetween(95, 100),
//     date: dayjs().subtract(index, "day").format("YYYY-MM-DD")
//   };
// });

//获取最新动态数据

let tableData = [{
  id: 1,
  requiredNumber: 135001,
  questionNumber: 126001,
  resolveNumber: 13500,
  satisfaction: 95,
  mailNumber: 10,
  bagNumber: 20,
  date: "2024-08-08"
}, {
  id: 2,
  requiredNumber: 13700,
  questionNumber: 12600,
  resolveNumber: 13500,
  satisfaction: 95,
  mailNumber: 100,
  bagNumber: 20,
  date: "2024-08-03"
}, {
  id: 3,
  requiredNumber: 13500,
  questionNumber: 12600,
  resolveNumber: 13500,
  satisfaction: 95,
  mailNumber: 115,
  bagNumber: 201,
  date: "2024-08-02"
}, {
  id: 4,
  requiredNumber: 13500,
  questionNumber: 12600,
  resolveNumber: 13500,
  satisfaction: 95,
  mailNumber: 182,
  bagNumber: 209,
  date: "2024-08-01"
}]



/** 最新动态 */
const latestNewsData = cloneDeep(tableData)
  .slice(0, 14)
  .map((item, index) => {
    return Object.assign(item, {
      // date: `${dayjs().subtract(index, "day").format("YYYY-MM-DD")} ${
      //   days[dayjs().subtract(index, "day").day()]
      // }`
      date: item.date
    });
  });

export { chartData, barChartData, progressData, tableData, latestNewsData };
