let flag = navigator.userAgent.match(
    /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i
);
export default {
    path: "/confirmation",
    // redirect: "/abnormalTable",
    component: () => import("@/views/Mobile/confirmation/index.vue"),
    meta: {
        icon: "fluent-mdl2:waitlist-confirm-mirrored",
        // showLink: flag ? true : false,
        title: "装车确认",
        rank: 14
    }
} satisfies RouteConfigsTable;
