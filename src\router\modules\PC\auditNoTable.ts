import { flag } from "@/router/utils";
export default {
    path: "/auditNoTable",
    redirect: "/auditNoTable/index",
    meta: {
        icon: "ri:calendar-todo-fill",
        title: "出库序列号表",
        rank: 3
    },
    children: [
        {
            path: "/auditNoTable/index",
            name: "auditNoTable",
            component: () => import("@/views/PC/auditNoTable/index.vue"),
            meta: {
                showLink: flag ? false : true,
                title: "出库序列号表",
            }
        },
        {
            path: "/auditNoTable/update",
            name: "updateAuditno",
            component: () => import("@/views/PC/auditNoTable/components/update.vue"),
            meta: {
                showLink: false,
                title: "出库序列号表",
            }
        },
    ]
} satisfies RouteConfigsTable;
