<script setup lang="ts">
import { ref } from "vue";
import leftLine from "@iconify-icons/ri/arrow-left-s-line";
import { type PlusColumn, type FieldValues, PlusForm } from "plus-pro-components";
import { useRouter, useRoute } from "vue-router";
import { message } from "@/utils/message";
import Cookies from "js-cookie";
import { axios_hg, axios_gh } from "@/api/user";

const router = useRouter();
const route = useRoute();

let form_columns: PlusColumn[] = [
    {
        label: "出库序列号",
        prop: "V_AUDIT_NO",
        fieldProps: {
            placeholder: "请输出库序列号",
            disabled: true
        }
    },
    {
        label: "车牌号",
        prop: "V_CARNO",
        fieldProps: {
            placeholder: "请输入车牌号"
        }
    },
    {
        label: "目的地",
        prop: "V_ADDRESS",
        fieldProps: {
            placeholder: "请输入目的地"
        }
    },
    {
        label: "类型",
        prop: "V_AUDIT_TYPE",
        valueType: "select",
        fieldProps: {
            placeholder: "全部"
        },
        options: [
            {
                label: "全部",
                value: "",
            },
            {
                label: "转场",
                value: "1",
            },
            {
                label: "转关",
                value: "2",
            },
            {
                label: "转运",
                value: "3",
            }
        ]
    },
    {
        label: "邮袋清单",
        prop: "V_BAGNO",
        valueType: "textarea",
        fieldProps: {
            placeholder: "请输入邮袋（用,隔开）",
            autosize: { minRows: 6, }
        }
    },
    {
        label: "总数量",
        prop: "V_MAILBAG_NUM",
        fieldProps: {
            placeholder: "请输入邮袋数量",
            disabled: true
        }
    },
    {
        label: "总重量",
        prop: "V_MAILBAG_WEIGHT",
        fieldProps: {
            placeholder: "请输入总重量",
            disabled: true
        }
    },
    {
        label: "关区号",
        prop: "CUSTOMS_CODE",
        fieldProps: {
            placeholder: "请输入关区号",
            disabled: true,
        }
    },
    {
        label: "操作人",
        prop: "V_OPERNAME",
        fieldProps: {
            placeholder: "请输入操作人",
            disabled: true,
        },
    },
];

const rules = {
    V_CARNO: [
        {
            required: true,
            message: "请输入车牌号"
        }
    ],
    V_AUDIT_TYPE: [
        {
            required: true,
            message: "请选择类型"
        }
    ],
    V_ENT_NAME: [
        {
            required: true,
            message: "请输入企业名称"
        }
    ]
};

let carInfo = ref<FieldValues>({
    V_AUDIT_NO: route.query.auditNo,
    V_CARNO: route.query.carNo,
    V_ADDRESS: route.query.address,
    V_AUDIT_TYPE: route.query.type,
    V_BAGNO: route.query.bagno,
    V_MAILBAG_NUM: route.query.num,
    V_MAILBAG_WEIGHT: route.query.weight,
    CUSTOMS_CODE: route.query.code,
    V_OPERNAME: Cookies.get("USER_NAME")
});

const handleReset = () => {
    // console.log("handleReset");
    message('重置成功', { type: 'success' })
};

const handleSubmit = (info: any) => {
    carUpdateReport(info.V_CARNO, info.V_ENT_TYPE, info.V_ENT_NAME).then((res: any) => {
        let data = res.result.info[0]
        if (data.flag == '1') {
            message('(海关接口)修改成功', { type: 'success' })
           // updateCarInfo(info)
            updateAuditNo2(info)
        } else {
            message('(海关接口)修改失败', { type: 'error' })
        }
    }).catch((err: any) => {
        console.log(err);
        message('(海关接口)修改失败, 原因：' + err, { type: 'error' })
    })

};

//海关-车辆信息备案修改
function carUpdateReport(carNo: string, entType: string, entName: string) {
    let params = [Cookies.get("USER_NAME")]
    params.unshift(entName)
    params.unshift(entType)
    params.unshift(carNo)
    let new_params = (JSON.stringify(params)).replace(/\"/g, "'")
    return axios_hg({
        service: 'RetpostMailService',
        method: 'CarUpdateReport',
        params: new_params
    })
}

//修改车辆信息
function updateCarInfo(info: any) {
    axios_gh('/clxxxg', info).then((res: any) => {
        console.log(res);
    }).catch((err: any) => {
        console.log(err);
    })
}

//修改备案信息
function updateAuditNo2(info: any) {
    axios_gh('/updateauditno2', info).then((res: any) => {
        console.log(res);
        // 添加成功提示
        if (res.code === 200) {
            message('备案修改成功', { type: 'success' })
        }
    }).catch((err: any) => {
        console.log(err);
        // 添加错误提示
        message('备案修改失败', { type: 'error' })
    })
}

//海关-装车出库申请修改接口
function outbandAuditUpdateReport(auditNo: string, carNo: string, address: string, operator: string,
    bagNum: string, bags: string, bagWeight: string, auditType: string, customsCode: string, applicant: string) {


}


function onBackClick() {
    router.go(-1)
}

</script>



<template>
    <div class="updateCar">
        <el-container>
            <el-main>
                <div class="flex items-center">
                    <el-button class="mb-[20px] float-right" @click="onBackClick">
                        <IconifyIconOffline :icon="leftLine" />返回
                    </el-button>
                </div>
                <PlusForm v-model="carInfo" class="w-[450px] m-auto" :columns="form_columns" :rules="rules"
                    label-position="right" @submit="handleSubmit" submit-text="修改" @reset="handleReset" reset-text="重置"
                    label-width="100">
                </PlusForm>
            </el-main>
        </el-container>
    </div>
</template>