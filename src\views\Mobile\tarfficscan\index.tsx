import { axios_gh, axios_hg } from "@/api/user";
import { ref, reactive, onMounted } from 'vue'
import { showToast, showFailToast, showNotify } from 'vant'
import Cookies from 'js-cookie';

export function useColumns() {
    let searchValue = ref<string>()
    let searchRef = ref<HTMLInputElement | null>(null)
    let show = ref<boolean>(true)


    interface form {
        V_BAGNO: string,
        V_OPERNAME: string
    }

    let formData: any = reactive({
        V_BAGNO: '',
        // V_OPERNAME: homeStore.user.PERSON_NAME
        V_OPERNAME: Cookies.get("USER_NAME")
    })


    onMounted(() => {
        searchRef.value && searchRef.value.focus()
    })

    function onSearch(val: string) {
        //两种总包类型
        let pattern1 = /^[0-9]{30}$/
        let pattern2 = /^[A-Z]{15}[0-9]{14}$/
        //判断是否符合规则
        if (pattern1.test(val) || pattern2.test(val)) {
            formData.V_BAGNO = val
        } else {
            showFailToast('不符合邮件号规则');
        }
        searchRef.value && searchRef.value.focus()
        searchValue.value = ''
    }

    function onSubmit() {
        mailTrafficScanReport()
    }

    //海关-退运邮件交航扫描申报接口
    function mailTrafficScanReport() {
        let params = []
        for (let key in formData) {
            params.push(formData[key])
        }
        let new_params = (JSON.stringify(params)).replace(/\"/g, "'")
        axios_hg(
            {
                service: 'RetpostMailService',
                method: 'mailTrafficScanReport',
                params: new_params
            }
        ).then((res: any) => {
            console.log(res)
            let data = res.result.info[0]
            let retArray = data.mailbagRet
            if (retArray[0] == 'success') {
                updateBagJhflag(data.info[0])
                showToast(retArray[1])
            } else {
                showNotify(retArray[1] + ',' + retArray[2])
                inserterror(res.result.info, res.result.status, res.result.note)
            }
        }).catch((err: any) => {
            console.log(err)
            showNotify('(海关接口)出现错误' + err)
            inserterror(err.result.info, err.result.status, err.result.note)
        })
    }
    function inserterror(V_INFO: string | any[], V_STATUS: string, V_NOTE: string) {
        // 处理 V_INFO 是数组的情况
        let processedInfo = V_INFO;
        if (Array.isArray(V_INFO)) {
            processedInfo = V_INFO.map(item => {
                return `邮件号: ${item.mailNo}, 原因: ${item.mailRet}`;
            }).join('; ');
        }

        axios_gh('/errorresult',
            qs.stringify(
                {
                    V_INFO: processedInfo,
                    V_STATUS: V_STATUS,
                    V_NOTE: V_NOTE
                },
                { indices: false })
        ).then((res: any) => {
            console.log(res)
        }).catch((err: any) => {
            console.log(err)
            showNotify({ message: '出现错误， 原因：' + err })
        })
    }
    //更新交航邮袋信息
    function updateBagJhflag(params: any) {
        axios_gh('/updatebagjhflag', {
            V_BAGNO: params.mailbagNo,
            V_MAILBAGRET: params.mailbagRet
        }).then((res: any) => {
            console.log(res)

        }).catch((err: any) => {
            console.log(err)

        })
    }
    return {
        searchValue,
        searchRef,
        formData,
        onSearch,
        onSubmit
    };
}