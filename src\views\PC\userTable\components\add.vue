


<script setup lang="ts">
import { onMounted, ref, toRaw, nextTick } from "vue";
import { useRouter } from "vue-router";
import "plus-pro-components/es/components/search/style/css";
import { type PlusColumn, type FieldValues, PlusForm } from "plus-pro-components";
import leftLine from "@iconify-icons/ri/arrow-left-s-line";
import { message } from "@/utils/message";
import { axios_gh, axios_hg } from "@/api/user";
import md5 from 'js-md5';


// 表单的引用
const formRef = ref(null);
// 重置表单内容
const handleFormReset = () => {
    if (formRef.value) {
        formRef.value.resetFields();  // 调用 resetFields 方法重置表单
    }
};

const router = useRouter();

let form_columns: PlusColumn[] = [
    {
        label: "员工名",
        prop: "V_USER_NAME",
        fieldProps: {
            placeholder: "请输入员工名"
        }
    },
    {

        label: "账号",
        prop: "V_LOGIN_CODE",
        fieldProps: {
            placeholder: "请输入账号"
        }
    },
    {

        label: "密码",
        prop: "V_LOGIN_PSW",
        // tooltip: '密码长度至少为8',
        fieldProps: {
            placeholder: "请输入密码"
        }
    },
    {
        label: "角色",
        prop: "V_ROLE_ID",
        valueType: "checkbox",
        options: async () => {
            const response = await axios_gh('/getrole');
            let data: any = response.data;
            return data.map((item: any) => ({
                label: item.V_ROLE_NAME,
                value: item.V_ROLE_ID
            }));
        }
    }
    // {
    //     label: "创建人",
    //     prop: "V_OPERNAME",
    //     fieldProps: {
    //         placeholder: "请输入操作人",
    //         disabled: true,
    //     },
    // },
];


const validatePass = (rule: any, value: string) => {
    // const regex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*[@$!%*?&+=.-/,|]).+$/
    const regex = /^(?=.*[A-Za-z])(?=.*[@$!%*?&+=.-/,|]).+$/
    if (value.length < 8) {
        return Promise.reject("密码长度至少为8位");
    }
    if (!regex.test(value)) {
        return Promise.reject("密码至少包含字母、数字、符号");
    }
}



const rules = {
    V_USER_NAME: [
        {
            required: true,
            message: "请输入员工名"
        }
    ],
    V_LOGIN_CODE: [
        {
            required: true,
            message: "请输入员工账户"
        }
    ],
    V_LOGIN_PSW: [
        {
            // required: true,
            // message: "请输入密码,长度至少为8位",
            // min: 8,
            validator: validatePass
        }
    ],
};


let userInfo = ref<FieldValues>({
    V_USER_NAME: "",
    V_LOGIN_CODE: "",
    V_LOGIN_PSW: "",
    V_ROLE_ID: "",
});


// const handleChange = (values: FieldValues, prop: PlusColumn) => {
//     console.log(values, prop, "change");
// };

// const handleReset = () => {
//     userInfo = ref<FieldValues>({
//         V_USER_NAME: "",
//         V_LOGIN_CODE: "",
//         V_LOGIN_PSW: "",
//         V_ROLE_ID: ""
//     });
        
//     message('重置成功', { type: 'success' })
// };


const handleReset = () => {
    userInfo.value = {
        V_USER_NAME: "",
        V_LOGIN_CODE: "",
        V_LOGIN_PSW: "",
        V_ROLE_ID: []
    };
    if (formRef.value) {
        formRef.value.resetFields(); // 调用 resetFields 方法重置表单
    }
    message('重置成功', { type: 'success' })
};
// const handleSubmit = (info: any) => {
//     console.log('Submit clicked');
//     let new_userInfo = toRaw(info);
//     new_userInfo.V_LOGIN_PSW = md5(info.V_LOGIN_PSW).toUpperCase();
//     console.log(new_userInfo);
//     let roleid = ""
//     for (let i = 0; i < toRaw(info.V_ROLE_ID).length; i++) {
//         roleid += info.V_ROLE_ID[i] + ","
//     }
//     new_userInfo.V_ROLE_ID = roleid;
//     console.log(new_userInfo);
//     axios_gh('/addaccount', new_userInfo).then((res: any) => {
//         console.log(res);
//         if (res.success === true) {
//             message('新增成功', { type: 'success' })
//         } else {
//             message('新增失败', { type: 'error' })
//         }
//         router.push('/userTable');
//     }).catch((err: any) => {
//         console.log(err);
//         message('出现错误' + err, { type: 'error' })
//     });
// };




// 表单校验和提交逻辑
const handleSubmit = () => {
    // 直接对 userInfo 执行验证检查
    const valid = validateForm(userInfo.value);

    if (valid) {
        let new_userInfo = { ...userInfo.value };
        new_userInfo.V_LOGIN_PSW = md5(userInfo.value.V_LOGIN_PSW).toUpperCase();
        let roleid = userInfo.value.V_ROLE_ID.join(",");
        new_userInfo.V_ROLE_ID = roleid;

        axios_gh('/addaccount', new_userInfo)
            .then((res: any) => {
                if (res.success === true) {
                    message('新增成功', { type: 'success' });
                    router.push('/userTable');
                } else {
                    message('新增失败', { type: 'error' });
                }
            })
            .catch((err: any) => {
                message('出现错误' + err, { type: 'error' });
            });
    } else {
        message('表单校验失败，请检查输入', { type: 'error' });
    }
};

onMounted(() => {
    getrole();
})
// 示例验证函数
const validateForm = (formValues) => {
    // 在这里添加你的自定义验证逻辑
    const { V_USER_NAME, V_LOGIN_CODE, V_LOGIN_PSW, V_ROLE_ID } = formValues;

    if (!V_USER_NAME || !V_LOGIN_CODE || V_LOGIN_PSW.length < 8 || !V_ROLE_ID.length) {
        return false;  // 验证失败
    }

    // 根据需要添加更多验证检查
    return true;  // 验证通过
};


//获取用户角色
async function getrole() {
    return await axios_gh("/getrole")
}



function onBackClick() {
    router.go(-1)
}



</script>

<template>
    <div class="addCar">
        <el-container>
            <el-main>
                <div class="flex items-center">
                    <el-button class="mb-[20px] float-right" @click="onBackClick">
                        <IconifyIconOffline :icon="leftLine" />返回
                    </el-button>
                </div>
                <PlusForm ref="formRef" v-model="userInfo" class="w-[450px] m-auto" :columns="form_columns"
                    :rules="rules" label-position="right" label-width="100">
                    <!-- 使用插槽自定义按钮，替代默认按钮 -->
                    <template #footer>
                        <div class="flex justify-center mt-[20px]">
                            <el-button type="primary" @click="handleSubmit(userInfo)">新增</el-button>
                            <el-button class="ml-[10px]" @click="handleReset">重置</el-button>
                        </div>
                    </template>
                </PlusForm>

            </el-main>
        </el-container>
    </div>
</template>
