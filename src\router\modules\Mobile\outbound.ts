let flag = navigator.userAgent.match(
    /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i
);
export default {
    path: "/outbound",
    // redirect: "/abnormalTable",
    component: () => import("@/views/Mobile/outbound/index.vue"),
    meta: {
        icon: "icon-park-outline:outbound",
        // showLink: flag ? true : false,
        title: "出库申请",
        rank: 12
    }
} satisfies RouteConfigsTable;
