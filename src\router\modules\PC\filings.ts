import { flag } from "@/router/utils";
export default {
    path: "/filings",
    redirect: "/filings/index",
    meta: {
        icon: "ri:car-fill",
        title: "车辆备案表",
        rank: 6
    },
    children: [
        {
            path: "/filings/index",
            name: "filings",
            component: () => import("@/views/PC/filings/index.vue"),
            meta: {
                showLink: flag ? false : true,
                title: "车辆备案表",
            }
        },
        {
            path: "/filings/add",
            name: 'addFilings',
            component: () => import("@/views/PC/filings/components/add.vue"),
            meta: {
                // icon: "mdi:car",
                showLink: false,
                title: "新增车辆",
                // rank: 105
            }
        },
        {
            path: "/filings/update",
            name: 'updateFilings',
            component: () => import("@/views/PC/filings/components/update.vue"),
            meta: {
                // icon: "mdi:car",
                showLink: false,
                title: "修改车辆",
                // rank: 105
            }
        },
    ]
} satisfies RouteConfigsTable;
