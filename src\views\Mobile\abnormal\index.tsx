import qs from 'qs'
import { axios_gh, axios_hg } from "@/api/user";
import { ref, reactive, onMounted, toRaw, getCurrentInstance } from 'vue'
import { showToast, showFailToast, showNotify, showLoadingToast, closeToast, showDialog } from 'vant'
import Cookies from 'js-cookie';

export function useColumns() {
    let searchValue = ref<string>()
    let searchRef = ref<HTMLInputElement | null>(null)
    let abNo = ref<string>('')
    let fileList = ref<Array<object>>([])
    let batchMode = ref<boolean>(false) // 批量模式开关
    let batchInput = ref<string>('') // 批量输入框
    let mailQueue = ref<string[]>([]) // 邮件队列
    let processing = ref<boolean>(false) // 是否正在处理
    let processedCount = ref<number>(0) // 已处理数量
    let successCount = ref<number>(0) // 成功数量



    interface cell {
        V_MAILNO: string,
        V_ABNORMALTYPE: string,
        V_OPERATORNAME: string,
        V_ENTNAME: string,
        V_ABINFO: string,
        V_INSTRSTATE: string,
        V_FILENAME: string,
        V_FILEDATA: string
    }

    let cell: cell = reactive({
        V_MAILNO: '',
        V_ABNORMALTYPE: '',
        // V_OPERATORNAME: homeStore.user.PERSON_NAME,
        V_OPERATORNAME: Cookies.get("USER_NAME"),
        V_ENTNAME: '中国邮政速递物流股份有限公司广东省分公司',
        V_ABINFO: '',
        V_INSTRSTATE: '',
        V_FILENAME: '',
        V_FILEDATA: ''
    })

    let showAbinfoPicker = ref<boolean>(false)
    let showCustomAbinfoInput = ref<boolean>(false); // 是否显示自定义输入框
    // let abinfoColumns = [
    //     { text: '安检拉下', value: '安检拉下' },
    //     { text: '截留邮件', value: '截留邮件' },
    //     { text: '放弃包', value: '放弃包' },
    //     { text: '无邮路', value: '无邮路' },
    //     { text: '根据邮监一科通知，该件需要清点，清点后移交缉私', value: '根据邮监一科通知，该件需要清点，清点后移交缉私' },
    //     { text: '自定义输入', value: '自定义输入' } // 新增自定义输入选项
    // ]
    let abinfoColumns = ref([
        { text: '安检拉下', value: '安检拉下' },
        { text: '截留邮件', value: '截留邮件' },
        { text: '放弃包', value: '放弃包' },
        { text: '无邮路', value: '无邮路' },
        { text: '境外二次进口', value: '境外二次进口' },
        { text: '根据邮监一科通知，该件需要清点，清点后移交缉私', value: '根据邮监一科通知，该件需要清点，清点后移交缉私' },
        { text: '自定义输入', value: '自定义输入' } // 新增自定义输入选项
    ])

    let showAbnormalTypePicker = ref<boolean>(false)
    let abnormalTypeColums = [
        { text: '退运已入库转其他原因出库', value: '1' },
        { text: '退运已装袋转退运已入库', value: '2' },
        { text: '退运已出库转退运已入库', value: '3' },
    ]

    function onAbinfoConfirm({ selectedOptions }: any) {
        const selectedValue = selectedOptions[0].value;
        if (selectedValue === '自定义输入') {
            cell.V_ABINFO = ''; // 清空默认值
            showCustomAbinfoInput.value = true; // 显示自定义输入框
        } else {
            cell.V_ABINFO = selectedValue;
            showCustomAbinfoInput.value = false; // 隐藏自定义输入框
        }
        showAbinfoPicker.value = false;
    }


    //类型选择确定
    // function onAbinfoConfirm({ selectedOptions }: any) {
    //     cell.V_ABINFO = selectedOptions[0].value
    //     showAbinfoPicker.value = false
    // }


    //类型选择确定
    function onAbnormalTypeConfirm({ selectedOptions }: any) {
        cell.V_ABNORMALTYPE = selectedOptions[0].value
        showAbnormalTypePicker.value = false
    }

    // 单条邮件号输入处理
    function onSearch(val: string) {
        if (batchMode.value) {
            showFailToast('批量模式已启用，请使用批量输入框');
            return;
        }

        //邮件号规则
        let pattern = /^[A-Z]{2}[0-9]{9}[A-Z]{2}$/
        //判断是否符合规则
        if (pattern.test(val)) {
            cell.V_MAILNO = val
        } else {
            showFailToast('不符合邮件号规则');
        }
        searchRef.value && searchRef.value.focus()
        searchValue.value = ''
    }

    // 批量输入处理
    function processBatchInput() {
        if (!batchInput.value.trim()) {
            showFailToast('请输入邮件号列表');
            return;
        }

        // 批量模式下的表单验证
        if (!cell.V_ABNORMALTYPE) {
            showFailToast('请先选择处置类型');
            showAbnormalTypePicker.value = true;
            return;
        }
        if (!cell.V_ABINFO) {
            showFailToast('请先选择情况说明');
            showAbinfoPicker.value = true;
            return;
        }

        // 分割输入内容，支持换行、逗号、分号分隔
        const mails = batchInput.value.split(/[\n,;]/)
            .map(mail => mail.trim())
            .filter(mail => mail);

        if (mails.length === 0) {
            showFailToast('未检测到有效的邮件号');
            return;
        }

        // 验证邮件号格式
        const pattern = /^[A-Z]{2}[0-9]{9}[A-Z]{2}$/;
        const invalidMails = mails.filter(mail => !pattern.test(mail));

        if (invalidMails.length > 0) {
            showDialog({
                title: '格式错误',
                message: `以下邮件号格式不正确:\n${invalidMails.join('\n')}`,
                confirmButtonText: '确定'
            });
            return;
        }

        mailQueue.value = [...mails]; // 创建副本
        processedCount.value = 0;
        successCount.value = 0;
        processing.value = true;
        processNextMail();
    }

    // 处理队列中的下一个邮件
    async function processNextMail() {
        if (mailQueue.value.length === 0) {
            processing.value = false;
            showToast(`批量处理完成: ${successCount.value}成功 ${processedCount.value - successCount.value}失败`);
            // 清空批量输入框
            batchInput.value = '';
            return;
        }

        const currentMail = mailQueue.value.shift()!;
        cell.V_MAILNO = currentMail;

        try {
            // 调用修改后的邮件申报函数，返回Promise
            await mailAbnormalDisposeAddReportAsync();
            processedCount.value++;
            successCount.value++;
        } catch (error) {
            processedCount.value++;
            console.error(`邮件 ${currentMail} 处理失败:`, error);
            // 不显示每个失败的toast，避免过多弹窗
        }

        // 处理下一个，添加延迟避免频繁请求
        setTimeout(processNextMail, 1000);
    }

    //申报
    function onSumbitClick() {
        // 表单验证
        if (!cell.V_MAILNO) {
            showFailToast('请输入邮件号');
            searchRef.value?.focus();
            return;
        }
        if (!cell.V_ABNORMALTYPE) {
            showFailToast('请选择异常类型');
            showAbnormalTypePicker.value = true;
            return;
        }
        if (!cell.V_ABINFO) {
            showFailToast('请输入异常信息');
            showAbinfoPicker.value = true;
            return;
        }

        // 设置默认值（如果用户未选择）
        if (!cell.V_INSTRSTATE) {
            cell.V_INSTRSTATE = '待处理';
        }

        mailAbnormalDisposeAddReport();
    }

    // 重置表单状态（仅重置邮件号）
    function resetForm() {
        // 仅重置邮件号字段
        cell.V_MAILNO = '';

        // 保留其他所有字段不变：
        // - V_ABNORMALTYPE
        // - V_OPERATORNAME
        // - V_ENTNAME
        // - V_ABINFO
        // - V_INSTRSTATE
        // - V_FILENAME
        // - V_FILEDATA

        // 保留文件上传状态
        // fileList.value 保持不变

        // 保留UI状态：
        // - showCustomAbinfoInput.value
        // - showAbinfoPicker.value
        // - showAbnormalTypePicker.value

        // 重置搜索焦点
        searchValue.value = '';
        searchRef.value?.focus();
    }

    //异常邮件处置申报
    function mailAbnormalDisposeAddReport() {
        let params = []
        for (let key in cell) {
            params.push(cell[key])
        }
        // params[params.length - 1] = encode
        let new_params = (JSON.stringify(params)).replace(/\"/g, "'")
        axios_hg({
            service: 'RetpostMailService',
            method: 'mailAbnormalDisposeAddReport',
            params: new_params
        }).then((res: any) => {
            let data = res.result.info[0]
            let retArray = data.mailRet.split('-')
            if (retArray[0] == 'success') {
                abNo.value = data.abNo
                update(abNo.value)
                showToast(retArray[1])
                resetForm(); // 提交成功后重置表单
            } else {
                // 旧版错误提示（保留但注释掉）
                // showNotify(retArray[1] + ',' + retArray[2])

                // 新版弹窗错误提示
                showDialog({
                    title: '提交失败',
                    message: `服务器返回错误：
${retArray[1]}
${retArray[2] || ''}`,
                    theme: 'round-button',
                    confirmButtonText: '确定',
                    confirmButtonColor: '#1989fa',
                    className: 'error-dialog'
                });
                inserterror(res.result.info, res.result.status, res.result.note);
            }
        }).catch((err: any) => {
            console.log(err);
            // 旧版错误提示（保留但注释掉）
            // showNotify('(海关接口)出现错误，原因：' + err)

            // 新版弹窗错误提示
            showDialog({
                title: '提交错误',
                message: `海关接口调用失败，详细信息：
原因: ${err.message || '未知错误'}
错误代码: ${err.code || '无'}
建议: 请检查网络连接后重试，或联系管理员`,
                theme: 'round-button',
                confirmButtonText: '确定',
                confirmButtonColor: '#1989fa',
                className: 'error-dialog'
            }).then(() => {
                cell.V_MAILNO = ''; // 错误时重置邮件号
                searchRef.value?.focus(); // 自动聚焦到输入框
            });
            inserterror(err.result?.info, err.result?.status, err.result?.note);
        })
    }

    // 异步版本的邮件申报函数，用于批量处理
    function mailAbnormalDisposeAddReportAsync(): Promise<void> {
        return new Promise((resolve, reject) => {
            let params = []
            for (let key in cell) {
                params.push(cell[key])
            }
            let new_params = (JSON.stringify(params)).replace(/\"/g, "'")

            axios_hg({
                service: 'RetpostMailService',
                method: 'mailAbnormalDisposeAddReport',
                params: new_params
            }).then((res: any) => {
                let data = res.result.info[0]
                let retArray = data.mailRet.split('-')
                if (retArray[0] == 'success') {
                    abNo.value = data.abNo
                    update(abNo.value)
                    // 批量模式下不显示每个成功的toast
                    // 只重置邮件号，保留其他字段用于下一个邮件
                    cell.V_MAILNO = '';
                    resolve();
                } else {
                    inserterror(res.result.info, res.result.status, res.result.note);
                    reject(new Error(`${retArray[1]} ${retArray[2] || ''}`));
                }
            }).catch((err: any) => {
                console.log(err);
                inserterror(err.result?.info, err.result?.status, err.result?.note);
                reject(err);
            });
        });
    }
    //插入异常情况
    function inserterror(V_INFO: string | any[], V_STATUS: string, V_NOTE: string) {
        // 处理 V_INFO 是数组的情况
        let processedInfo = V_INFO;
        if (Array.isArray(V_INFO)) {
            processedInfo = V_INFO.map(item => {
                return `邮件号: ${item.mailNo}, 原因: ${item.mailRet}`;
            }).join('; ');
        }

        axios_gh('/errorresult',
            qs.stringify(
                {
                    V_INFO: processedInfo,
                    V_STATUS: V_STATUS,
                    V_NOTE: V_NOTE
                },
                { indices: false })
        ).then((res: any) => {
            console.log(res)
        }).catch((err: any) => {
            console.log(err)
            showNotify({ message: '出现错误， 原因：' + err })
        })
    }

    function update(abNo: string) {
        // 创建一个新对象，而不是直接修改 cell
        const updateData = {
            ...toRaw(cell),
            V_ABNO: abNo
        };

        axios_gh('/ycyjczcr', updateData)
            .then((res: any) => {
                console.log(res)
            })
            .catch((err: any) => {
                console.log(err)
            });
    }

    //图片上传
    // function afterRead(file: any) {
    //     let fileObject = toRaw(file)
    //     cell.V_FILENAME = fileObject.file.name
    //     let contentArray = fileObject.content.split(',')
    //     cell.V_FILEDATA = fileObject.content



    //     // let temp = fileObject.content.split(',')
    //     // let mime = temp[0].match(/:(.*?);/)[1]
    //     // let raw = window.atob(temp[1])

    //     // let rawLength = raw.length

    //     // let uInt8Array = new Uint8Array(rawLength)
    //     // console.log(uInt8Array);

    //     // for(let i = 0; i < rawLength; i++) {
    //     //     uInt8Array[i] = raw.charCodeAt(i)
    //     // }
    //     // let blob = new Blob([uInt8Array], {type: mime})
    //     // console.log(blob);

    //     // let filenew = new File([blob], '1', {type: mime, lastModified:Date.now()})
    //     // console.log(filenew);


    //     // cell.V_FILENAME = fileObject.file.name
    //     // let contentArray = fileObject.content.split(",")

    //     // cell.V_FILEDATA = contentArray[1]



    //     // let imgBlob = URL.createObjectURL(fileObject.file)
    //     // console.log(imgBlob);


    // }

    function afterRead(file: any) {
        const fileObject = toRaw(file); // 确保文件对象为原始数据
        cell.V_FILENAME = fileObject.file.name; // 获取文件名
        const contentArray = fileObject.content.split(',');
        if (contentArray.length > 1) {
            cell.V_FILEDATA = contentArray[1]; // 提取 Base64 数据部分
        } else {
            showNotify('文件内容无效，请检查文件是否正确上传');
        }
    }


    //图片删除
    function deleteUpload() {
        cell.V_FILENAME = ""
        cell.V_FILEDATA = '';
    }

    function beforeRead(file: File): boolean {
        const allowedTypes = [
            'image/jpeg',
            'image/png',
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'text/plain',
        ];

        if (!allowedTypes.includes(file.type)) {
            showNotify('文件格式不支持，请上传常见图片、文档或表格格式');
            return false;
        }

        return true;
    }


    return {
        searchValue,
        searchRef,
        cell,
        abnormalTypeColums,
        showAbnormalTypePicker,
        showAbinfoPicker,
        showCustomAbinfoInput,
        abinfoColumns,
        afterRead,
        beforeRead,
        fileList,
        batchMode,
        batchInput,
        mailQueue,
        processing,
        processedCount,
        successCount,
        onAbnormalTypeConfirm,
        onAbinfoConfirm,
        onSearch,
        onSumbitClick,
        deleteUpload,
        processBatchInput
    };
}