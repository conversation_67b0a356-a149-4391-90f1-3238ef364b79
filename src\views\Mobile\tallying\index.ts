
import { ref, toRaw } from "vue";
import { axios_gh } from "@/api/user";
import "plus-pro-components/es/components/search/style/css";
import { type PlusColumn, PlusSearch } from "plus-pro-components";
import { showNotify, showFailToast, showToast, showDialog, showConfirmDialog } from 'vant';
import Cookies from "js-cookie";
export function useColumns() {
    let search = ref<string>()
    const tallyingList = ref<Array<any>>([])

    let searchRef = ref<HTMLInputElement | null>(null)

    const storageLocation = ref<string>(""); // 新增储位号变量

    //采集条码
    async function onSearch(val: string) {
        val = val.trim()
        //两种总包类型
        // let pattern1 = /^[0-9]{30}$/
        // let pattern2 = /^[A-Z]{15}[0-9]{14}$/
        
        // 规则一：以 CNCAN 开头，接 10 个大写字母，再接 14 个数字，共 29 位
        let pattern1 = /^CNCAN[A-Z]{10}\d{14}$/;

        // 规则二：以 51040 开头，总共 30 位数字，第 9 至第 16 位为 10000000 或 20000000
        let pattern2 = /^51040\d{3}(10000000|20000000)\d{14}$/;

        // 新增规则三：以 510400348 或 510400343 开头，总共 30 位数字
        let pattern3 = /^(510400348|510400343|510400345|510400342)\d{21}$/;

        

        if (!pattern1.test(val) && !pattern2.test(val) && !pattern3.test(val) ) {
            showFailToast('条码不符合规则');
        } else if (judgeRepeat(val)) {
            showFailToast('重复总包条码');
        } else if (await judgeDataBase(val)) {
            showFailToast('总包已经采集过');
        } else {
            autoSubmit(val)
        }
        search.value = ''
        searchRef.value && searchRef.value.focus()
    }

    //判断当前采集是否重复
    function judgeRepeat(val: string) {
        let result: boolean = false
        tallyingList.value.forEach((e: any) => {
            if (e === val) {
                result = true
            }
        })
        return result;
    }

    //判断数据库是否已经存在
    async function judgeDataBase(val: string) {
        let result: boolean = false
        await axios_gh('/select', {
            V_BAGNO: val,
        }).then((res: any) => {
            let data = res.data
            let len = data.length
            if (len > 0) {
                result = true
            }
        }).catch((err: any) => {
            console.log(err);
        })
        return result;
    }

    //自动提交，插入数据库
    function autoSubmit(bagno: string) {
        axios_gh('/cjzbtm', {
            V_BAGNO: bagno,
            // V_OPERATORNAME: homeStore.user.PERSON_NAME
            V_STORAGELOCATION: storageLocation.value, // 新增储位号
            V_OPERATORNAME: Cookies.get("USER_NAME")
        }).then((res: any) => {
            if (res.success = true) {
                tallyingList.value.unshift(bagno)
                searchRef.value && searchRef.value.focus()
                showToast('采集成功')
            } else {
                showNotify('采集失败')
            }
        }).catch((err: any) => {
            console.log(err);
            showNotify('采集失败' + err)
        })
    }


    return {
        search,
        searchRef,
        tallyingList,
        onSearch,
        storageLocation // 返回储位号变量
    };
}