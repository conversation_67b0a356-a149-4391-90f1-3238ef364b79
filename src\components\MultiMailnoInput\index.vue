<template>
  <div class="multi-mailno-input">
    <el-input
      type="textarea"
      v-model="inputValue"
      :placeholder="placeholder"
      :rows="rows"
      :maxlength="maxlength"
      @input="handleInput"
    />
    <div v-if="showCount" class="count-info">
      已输入 {{ mailnoCount }} 个邮件号
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: '请输入邮件号，支持批量'
  },
  rows: {
    type: Number,
    default: 3
  },
  maxlength: {
    type: Number,
    default: 2000
  },
  showCount: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['update:modelValue', 'change']);

const inputValue = ref(props.modelValue);

// 计算邮件号数量
const mailnoCount = computed(() => {
  if (!inputValue.value) return 0;
  return inputValue.value.split(/[\n,，\s]/).filter(item => item.trim()).length;
});

// 处理输入
const handleInput = (val: string) => {
  emit('update:modelValue', val);
  emit('change', val);
};

// 监听外部值变化
watch(() => props.modelValue, (newVal) => {
  inputValue.value = newVal;
});

// 组件挂载时确保初始值正确
onMounted(() => {
  if (props.modelValue) {
    inputValue.value = props.modelValue;
  }
});
</script>

<style scoped>
.multi-mailno-input {
  width: 100%;
}
.count-info {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
  text-align: right;
}
</style>
