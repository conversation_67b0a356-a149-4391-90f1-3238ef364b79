import { clone, delay } from "@pureadmin/utils";
import { ref, onMounted, reactive, watchEffect, toRaw } from "vue";
import type { PaginationProps, AdaptiveConfig, LoadingConfig, Align } from "@pureadmin/table";
import { showNotify, showFailToast, showToast, showDialog, showConfirmDialog } from 'vant';
import { utils, writeFile } from "xlsx";
import { message } from "@/utils/message";
import { axios_gh, axios_hg } from "@/api/user";
import "plus-pro-components/es/components/search/style/css";
import { type PlusColumn, PlusSearch } from "plus-pro-components";
import { addDialog } from "@/components/ReDialog";
import { IconifyIconOffline, IconifyIconOnline } from "@/components/ReIcon";
import Cookies from "js-cookie";
import router from "@/router";
import qs from "qs";
import dayjs from 'dayjs';
export function useColumns() {

    let columns: TableColumnList = [
        {
            type: "selection",
            align: "left",
            fixed: true,
        },
        {
            label: "出库序列号",
            prop: "V_AUDIT_NO",
            width: 170,
            fixed: true
        },
        {
            label: "申报时间",
            prop: "D_OPER_DATE",
            width: 110
        },
        {
            label: '运输类型',
            prop: 'V_AUDIT_TYPE',
            width: 130,
            cellRenderer: ({ row }) => {
                switch (row.V_AUDIT_TYPE) {
                    case '1':
                        return '转场';
                    case '2':
                        return '转关';
                    case '3':
                        return '转运';
                    default:
                        return '';
                }

            }
        },
        {
            label: '目的地',
            prop: 'V_ADDRESS',
            width: 160
        },
        {
            label: '车牌号',
            prop: 'V_CARNO',
            width: 160
        },
        {
            label: '总数量',
            prop: 'V_MAILBAG_NUM',
            width: 80,
            cellRenderer: ({ row }) => (
                <a style="font-size: 18px; color: #409eff" onClick={() => showDetailBagData(row)}>
                    {row.V_MAILBAG_NUM}
                </a>
            )
        },
        {
            label: '总重量(kg)',
            prop: 'V_MAILBAG_WEIGHT',
            width: 120
        },
        {
            label: '出库审核结果',
            prop: 'V_CHECK',
            width: 160,
            cellRenderer: ({ row }) => {
                switch (row.V_CHECK) {
                    case '0':
                        return '审核未通过';
                    case '1':
                        return '审核通过';
                    case '-1':
                        return '未进行审核';
                    default:
                        return '';
                }

            }
        },
        {
            label: '原因',
            prop: 'V_PASSDESC',
            width: 120,
            // renderHeader: () => {
            //     return <span style="display: flex">原因<IconifyIconOnline icon="ri:information-2-fill" width="18px" /></span>;
            // },
        },
        {
            label: '关锁号',
            prop: 'V_LOCK_NO',
            width: 120
        },
        {
            label: '司机纸号',
            prop: 'V_DRIVER_NO',
            width: 120
        },
        {
            label: '装车确认结果',
            prop: 'V_FLAG',
            width: 120,
            cellRenderer: ({ row }) => {
                if (row.V_FLAG == 1) {
                    return '成功'
                } else if (row.V_FLAG == 0) {
                    return '失败'
                } else {
                    return ''
                }

            }
        },
        {
            label: '操作人',
            prop: 'V_OPERNAME',
            width: 120
        },
        {
            label: "操作",
            width: "120",
            fixed: "right",
            slot: "operation"
        }

    ];

    let search_columns: PlusColumn[] = [
        {
            label: "出库序列号",
            prop: "V_AUDIT_NO",
            fieldProps: {
                placeholder: "请输入出库序列号"
            }
        },
        {
            label: "申报时间",
            prop: "D_OPER_DATE",
            valueType: "date-picker",
            fieldProps: {
                type: "daterange",
                startPlaceholder: "开始时间",
                endPlaceholder: "结束时间",
                valueFormat: "YYYY/MM/DD"
            }
        },
        {
            label: "操作人",
            prop: "V_OPERNAME",
            fieldProps: {
                placeholder: "请输入操作人"
            }
        },
        {
            label: "邮袋号",
            prop: "V_BAGNO",
            fieldProps: {
                placeholder: "请输入邮袋号"
            }
        },
    ];

    const dataList = ref([]);
    const loading = ref(true);
    const tableSize = ref("default");
    let multipleSelection = ref([]);
    // let tableData = [];

    //默认查询数据
    let auditno_select = ref({
        V_AUDIT_NO: '',
        D_OPER_DATE: [
            dayjs().format('YYYY/MM/DD'),  // 今天开始
            dayjs().format('YYYY/MM/DD')   // 今天结束
        ],
        V_OPERNAME: '',
        V_BAGNO: '',
    });



    /** 分页配置 */
    const pagination = reactive<PaginationProps>({
        pageSize: 10,
        currentPage: 1,
        pageSizes: [10, 50, 200, 500, 1000],
        total: 0,
        align: "center",
        background: true,
        small: false
    });


    /** 加载动画配置 */
    const loadingConfig = reactive<LoadingConfig>({
        text: "正在加载第一页...",
        viewBox: "-10, -10, 50, 50",
        spinner: `
    <path class="path" d="
        M 30 15
        L 28 17
        M 25.61 25.61
        A 15 15, 0, 0, 1, 15 30
        A 15 15, 0, 1, 1, 27.99 7.5
        L 15 15
    " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>
    `
        // svg: "",
        // background: rgba()
    });

    const handleChange = (values: any) => {
        console.log(values, "change");
    };
    //查询
    const handleSearch = (values: any) => {
        const searchParams = {
            ...values,
            D_OPER_DATE_START: values.D_OPER_DATE?.[0] || '',
            D_OPER_DATE_END: values.D_OPER_DATE?.[1] || '',
        };

        // 删除原始日期范围字段
        delete searchParams.D_OPER_DATE;

        getAuditNoData(toRaw(searchParams)).then((res) => {
            dataList.value = res;
            pagination.total = dataList.value.length;
            loading.value = false;
            message('查询成功', { type: 'success' })
        });
    };

    function onSizeChange(val: number) {
        message('更改为每页' + val + '条', { type: 'success', offset: 500 });
    }

    function onCurrentChange(val: number) {
        // console.log(val);

        // loadingConfig.text = `正在加载第${val}页...`;
        // loading.value = true;
        // delay(600).then(() => {
        //     loading.value = false;
        // });
    }

    const handleSelectionChange = (val: any) => {
        multipleSelection.value = val;
    };

    // 重置
    const handleRest = () => {
        auditno_select = ref({
            V_AUDIT_NO: '',
            D_OPER_DATE: '',
            V_OPERNAME: '',
            V_BAGNO: '',
        });
        handleSearch(auditno_select.value)
    };

    const exportExcel = () => {
        const res = dataList.value.map(item => {
            const arr = [];
            columns.forEach((column, index) => {
                arr.push(item[column.prop as string]);
            });
            return arr;
        });
        const titleList = [];
        columns.forEach((column, index) => {
            titleList.push(column.label);
        });
        res.unshift(titleList);
        const workSheet = utils.aoa_to_sheet(res);
        //设置列宽
        workSheet["!cols"] = [
            { wpx: 150 },
            { wpx: 100 },
            { wpx: 80 },
            { wpx: 150 },
            { wpx: 100 },
            { wpx: 60 },
            { wpx: 80 },
            { wpx: 100 },
            { wpx: 100 },
            { wpx: 100 },
            { wpx: 100 },
            { wpx: 100 },
            { wpx: 100 }
        ]
        const workBook = utils.book_new();
        utils.book_append_sheet(workBook, workSheet, "出库序列");
        writeFile(workBook, "出库序列表" + new Date().getTime() + ".xlsx");
        message("导出成功", {
            type: "success"
        });
    };

    //获取邮袋数据
    const getAuditNoData = (object?: any) => {
        return axios_gh('/cxxlhxx', object).then((res: any) => {
            let data = res.data
            for (let i = 0; i < data.length; i++) {
                data[i]["key"] = i
            }
            // tableData = []
            // tableData = data
            return data
            // message('查询成功', { type: 'success' })
        })
    }

    const getBagNoInfo = (object?: any) => {
        return axios_gh('/gjckxlhcxydh', object).then((res: any) => {
            let data = res.data
            for (let i = 0; i < data.length; i++) {
                data[i]["key"] = i
            }
            // tableData = []
            // tableData = data
            return data
            // message('查询成功', { type: 'success' })
        })
    }


    //显示详细总包数据

    function showDetailBagData(row) {
        let num = row.V_MAILBAG_NUM;
        const bagData = ref([] as any[]);
        const loading = ref(true);
        const errorMessage = ref(""); // 用于保存错误或空数据提示信息

        if (num > 0) {
            getBagNoInfo({ V_AUDIT_NO: row.V_AUDIT_NO }).then((data) => {
                console.log("Total Bag Info Data:", data);
                if (!data || data.length === 0) {
                    errorMessage.value = "没有找到相关数据";
                } else {
                    bagData.value = data;
                }
                loading.value = false;
            }).catch((error) => {
                console.error('获取总包信息失败:', error);
                errorMessage.value = "获取总包信息失败，请稍后再试。";
                loading.value = false;
            });

            addDialog({
                title: `详细总包数据(${num})`,
                width: 400,
                contentRenderer: () => {
                    if (loading.value) {
                        return <div>加载中...</div>;
                    }
                    if (errorMessage.value) {
                        return <div>{errorMessage.value}</div>;
                    }
                    return (
                        <div>
                            {bagData.value.map((item, index) => (
                                <span key={index}>
                                    {index + 1}、{item.V_BAGNO} &nbsp;&nbsp; {item.N_BAGWEIGHT} kg<br />
                                </span>
                            ))}
                            <br />
                        </div>
                    );
                }
            });
        } else {
            message('该序列号没有总包', { type: 'warning' });
        }
    }



    // function showDetailBagData(row) {
    //     let num = row.V_MAILBAG_NUM
    //     if (num > 0) {
    //         addDialog({
    //             title: '详细总包数据(' + num + ')',
    //             width: 400,
    //             // fullscreenIcon: true,
    //             // showClose: false,
    //             // fullscreenCallBack: ({ options, index }) =>
    //             //     message(options.fullscreen ? "全屏" : "非全屏"),
    //             contentRenderer: async () => {
    //                 try {
    //                     const data = await getBagNoInfo({ V_AUDIT_NO: row.V_AUDIT_NO });
    //                     console.log("Total Bag Info Data:", data);

    //                 // const bagno = data.map(item => item.V_BAGNO);
    //                 // const bagweight = data.map(item => item.N_BAGWEIGHT);
    //                 // let bagno = row.V_BAGNO.split(',')
    //                     // let bagweight = row.N_BAGWEIGHT.split(',')
    //                 if (data.length === 0) {
    //                     return <div>没有找到相关数据</div>;
    //                 }
    //                 return (
    //                     <div>
    //                         {data.map((item, index) => (
    //                             <span key={index}>
    //                                 {index + 1}、{item.V_BAGNO} &nbsp; &nbsp; &nbsp; {item.N_BAGWEIGHT}kg<br />
    //                             </span>
    //                         ))}
    //                         <br />
    //                     </div>
    //                 );
    //             } catch(error) {
    //                 console.error('获取总包信息失败:', error);
    //                 return <div>获取总包信息失败，请稍后再试。</div>;
    //             }
    //         }
    // });
    //     } else {
    //         message('该序列号没有总包', { type: 'warning' })
    //     }

    // }

    function inserterror(V_INFO: string | any[], V_STATUS: string, V_NOTE: string) {
        // 处理 V_INFO 是数组的情况
        let processedInfo = V_INFO;
        if (Array.isArray(V_INFO)) {
            processedInfo = V_INFO.map(item => {
                return `邮件号: ${item.mailNo}, 原因: ${item.mailRet}`;
            }).join('; ');
        }

        axios_gh('/errorresult',
            qs.stringify(
                {
                    V_INFO: processedInfo,
                    V_STATUS: V_STATUS,
                    V_NOTE: V_NOTE
                },
                { indices: false })
        ).then((res: any) => {
            console.log(res)
        }).catch((err: any) => {
            console.log(err)
            showNotify({ message: '出现错误， 原因：' + err })
        })
    }

    /**
     * 出库审核结果查询
     */
    function onExamineResultClick() {
        let data_array = toRaw(multipleSelection.value);
        let len = data_array.length;
        for (let i = 0; i < len; i++) {
            let data = data_array[i];
            // if (data.V_CHECK != '1') { //审核未通过和未进行审核的
            outbandAuditResultReport(data.V_AUDIT_NO).then((res: any) => {
                let data2 = res.result.info[0]
                let auditNo = data2.auditno

                let check = data2.check
                let passdesc = data2.passdesc || data2.desc
                if (data2.flag == '0') {
                    message(passdesc, { type: 'error' })

                } else {
                    message(passdesc, { type: 'success' })
                }
                updateAuditNo(data.V_AUDIT_NO, check, passdesc)
            }).catch((err: any) => {
                console.log(err)
                message('(海关接口)审核结果查询失败,原因：' + err, { type: 'error' })
            })
            // }
            // else {
            //     message('审核已通过！无需再查询审核结果', { type: 'success' })
            // }
        }
    }


    //海关-出库申请审核结果查询接口
    function outbandAuditResultReport(auditNo: string) {
        return axios_hg({
            service: 'RetpostMailService',
            method: 'outbandAuditResultReport',
            params: auditNo
        })
    }


    //出库申请审核结果更新
    function updateAuditNo(adudioNo: string, check: string, passdesc: string) {
        return axios_gh('/updateauditno', {
            V_AUDIT_NO: adudioNo,
            V_CHECK: check,
            V_PASSDESC: passdesc
        })
    }

    /**
     * 删除
     */
    function onDeleteClick(row) {
        let auditNo = row.V_AUDIT_NO
        let operator = Cookies.get("USER_NAME")
        outbandAuditDelecteReport(auditNo, operator).then((res: any) => {
            let data = res.result.info[0]
            if (data.flag == '1') {
                message('(海关接口)删除成功', { type: 'success' })
                delauditno(auditNo, operator)
            } else {
                message(data.desc, { type: 'error' })
            }
        }).catch((err: any) => {
            message('(海关接口)删除失败', { type: 'error' })
        })
    }


    //海关-装车出库申请删除接口
    function outbandAuditDelecteReport(auditNo: string, operator: string) {
        let params = [auditNo, operator]
        let new_params = (JSON.stringify(params)).replace(/\"/g, "'")
        return axios_hg({
            service: 'RetpostMailService',
            method: 'outbandAuditDelecteReport',
            params: new_params
        })
    }

    //数据库删除
    function delauditno(auditNo: string, operator: string) {
        return axios_gh('/delauditno', {
            V_AUDIT_NO: auditNo,
            V_OPERNAME: operator
        })
    }

    /**
     * 修改
     */
    function onUpdateClick(row) {
        console.log(row);

        router.push({
            path: '/auditNoTable/update',
            query: {
                auditNo: row.V_AUDIT_NO,
                carNo: row.V_CARNO,
                address: row.V_ADDRESS,
                type: row.V_AUDIT_TYPE,
                bagno: row.V_BAGNO,
                num: row.V_MAILBAG_NUM,
                weight: row.V_MAILBAG_WEIGHT,
                code: row.CUSTOMS_CODE,
            }
        })
    }

    function onConfirmOutboundClick() {
        let data_array = toRaw(multipleSelection.value);
        if (data_array.length === 0) {
            message("请选择要确认的记录", { type: "warning" });
            return;
        }

        for (let i = 0; i < data_array.length; i++) {
            let data = data_array[i];
            if (data.V_CHECK !== '1') {
                message(`序号 ${data.V_AUDIT_NO} 未通过审核，不能确认出库`, { type: "warning" });
                continue;
            }

            // 更新出库时间
            updateOutboundTime(data.V_AUDIT_NO).then((res: any) => {
                if (res.success && res.data === "1 rows affected") {
                    message(`序号 ${data.V_AUDIT_NO} 出库确认成功`, { type: "success" });
                } else {
                    message(`序号 ${data.V_AUDIT_NO} 出库确认失败`, { type: "error" });
                }
            }).catch((err: any) => {
                console.log(err);
                message(`序号 ${data.V_AUDIT_NO} 出库确认失败`, { type: "error" });
            });
        }
    }

    // 更新出库时间
    function updateOutboundTime(auditNo: string) {
        return axios_gh('/updateoutboundtime', {
            V_AUDIT_NO: auditNo
        });
    }

    function onClearBagClick(row) {
        let auditNo = row.V_AUDIT_NO;
        let operator = Cookies.get("USER_NAME");

        // 调用后端接口清空总包号
        return axios_gh('/clearauditbag', {
            V_AUDIT_NO: auditNo
        }).then((res: any) => {
            if (res.success) {
                message("清空总包成功", { type: "success" });
                // 刷新数据
                getAuditNoData(auditno_select.value).then((res: any) => {
                    dataList.value = res;
                    pagination.total = dataList.value.length;
                });
            } else {
                message("清空总包失败", { type: "error" });
            }
        }).catch((err: any) => {
            console.log(err);
            message("清空总包失败", { type: "error" });
        });
    }

    return {
        columns,
        search_columns,
        auditno_select,
        pagination,
        loadingConfig,
        dataList,
        loading,
        tableSize,
        handleChange,
        handleSearch,
        handleRest,
        onSizeChange,
        onCurrentChange,
        handleSelectionChange,
        exportExcel,
        getAuditNoData,
        onExamineResultClick,
        onDeleteClick,
        onUpdateClick,
        onConfirmOutboundClick,
        onClearBagClick
    };
}
