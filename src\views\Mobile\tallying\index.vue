<script setup lang="ts">
import "plus-pro-components/es/components/search/style/css";
import { reactive, toRaw, ref, onMounted, getCurrentInstance } from "vue";
import { useColumns } from "./index";
import { getMine } from "@/api/user";

const {
    search,
    searchRef,
    tallyingList,
    onSearch,
    storageLocation // 使用储位号变量
} = useColumns();

onMounted(() => {
    searchRef.value && searchRef.value.focus();
})

// getMine().then(res => {
//     console.log(res.data);

// });

</script>

<template>
    <div class="Tallying">
        <header>
            <van-search v-model="search" shape="round" background="#1989fa" placeholder="请输入总包条码" @search="onSearch"
                ref="searchRef" />
            <!-- 添加储位号输入框 -->
            <van-field v-model="storageLocation" clearable placeholder="请输入储位号" style="margin-top: 16px;" />
        </header>
        <main>
            <van-divider :style="{ color: '#1989fa', borderColor: '#1989fa', padding: '0 16px' }">
                成功采集的总包条码({{ tallyingList.length }})
            </van-divider>
            <div class="all">
                <van-cell-group>
                    <van-cell v-for="(item, index) in tallyingList" :key="index" :title="item">
                    </van-cell>
                </van-cell-group>

            </div>
        </main>
    </div>
</template>

<style scoped>
/*main {
    margin-top: 2vh;
}

.all {
    height: 50vh;
    overflow: scroll;
    border: 2px dotted #1989fa;
    margin-bottom: 2vh;
}

.van-cell {
    padding: 4px 0;
}*/
.Tallying {
    display: flex;
    flex-direction: column;
    height: 100%;
}

header {
    display: flex;
    flex-direction: column;
    /* 上下对齐 */
    align-items: stretch;
    /* 使元素充满整个宽度 */
    padding: 8px;
    background-color: #f5f5f5;
    border-bottom: 1px solid #eaeaea;
}

.search-field {
    margin-bottom: 8px;
    /* 添加间距 */
    width: 100%;
    /* 占满宽度 */
    background-color: #1989fa;
    border-radius: 16px;
    padding: 8px 16px;
    font-size: 16px;
    color: #ffffff;
}

main {
    margin-top: 2vh;
    flex: 1;
    overflow-y: auto;
}

.all {
    height: 50vh;
    overflow: scroll;
    border: 2px dotted #1989fa;
    margin-bottom: 2vh;
}

.van-cell {
    padding: 4px 0;
}
</style>