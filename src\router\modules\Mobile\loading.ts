let flag = navigator.userAgent.match(
    /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i
);
export default {
    path: "/loading",
    // redirect: "/abnormalTable",
    component: () => import("@/views/Mobile/loading/index.vue"),
    meta: {
        icon: "tabler:truck-loading",
        // showLink: flag ? true : false,
        title: "装车绑定",
        rank: 13
    }
} satisfies RouteConfigsTable;
