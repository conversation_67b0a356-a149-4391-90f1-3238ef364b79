<script setup lang="ts">
import { useDark, useECharts } from "@pureadmin/utils";
import { message } from "@/utils/message";
import { type PropType, ref, computed, watch, nextTick, onMounted } from "vue";
import { axios_gh, axios_hg } from "@/api/user";

const props = defineProps({
  time: {
    type: Array as PropType<Array<string>>,
    default: () => []
  },
  mailData: {
    type: Array as PropType<Array<number>>,
    default: () => []
  },
  bagData: {
    type: Array as PropType<Array<number>>,
    default: () => []
  }
});

const { isDark } = useDark();

const theme = computed(() => (isDark.value ? "dark" : "light"));

const chartRef = ref();
const { setOptions } = useECharts(chartRef, {
  theme
});

watch(
  () => props,
  async () => {
    await nextTick(); // 确保DOM更新完成后再执行
    setOptions({
      container: ".bar-card",
      color: ["#41b6ff", "#e85f33"],
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "none"
        }
      },
      grid: {
        top: "20px",
        left: "50px",
        right: 0
      },
      legend: {
        data: ["入库邮件", "理货总包"],
        textStyle: {
          color: "#606266",
          fontSize: "0.875rem"
        },
        bottom: 0
      },
      xAxis: [
        {
          type: "category",
          data: props.time,
          axisLabel: {
            fontSize: "0.875rem"
          },
          axisPointer: {
            type: "shadow"
          }
        }
      ],
      yAxis: [
        {
          type: "value",
          axisLabel: {
            fontSize: "0.875rem"
          },
          splitLine: {
            show: false // 去网格线
          }
          // name: "单位: 个"
        }
      ],
      series: [
        {
          name: "入库邮件",
          type: "bar",
          barWidth: 10,
          itemStyle: {
            color: "#41b6ff",
            borderRadius: [10, 10, 0, 0]
          },
          // data: props.requireData
          data: props.mailData
        },
        {
          name: "理货总包",
          type: "bar",
          barWidth: 10,
          itemStyle: {
            color: "#e86033ce",
            borderRadius: [10, 10, 0, 0]
          },
          // data: props.questionData
          data: props.bagData
        }
      ]
    });
  },
  {
    deep: true,
    immediate: true
  }
);

onMounted(() => {
  getToatalByDate();
})


async function getToatalByDate() {
  await axios_gh("/gettotalbydate").then((res: any) => {
    let data = res.data
    data.forEach((e: any) => {
      props.time.push(e.TIME);
      props.mailData.push(e.MAIL);
      props.bagData.push(e.BAG);
    })
  }).catch((err: any) => {
    console.log(err);
    message(err.message, { type: "error" })
  })
}



</script>

<template>
  <div ref="chartRef" style="width: 100%; height: 365px" />
</template>
