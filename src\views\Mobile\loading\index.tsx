import qs from 'qs'
import { axios_gh, axios_hg } from "@/api/user";
import { ref, reactive, onMounted, toRaw, getCurrentInstance } from 'vue'
import { showToast, showFailToast, showNotify, showLoadingToast, closeToast, showDialog } from 'vant'
import Cookies from 'js-cookie';

export function useColumns() {
    let instance: any = getCurrentInstance()
    let searchValue = ref<string>()
    let searchRef = ref<HTMLInputElement | null>(null)
    let showButton = ref<boolean>(true)

    interface adudioNo {
        V_AUDIT_NO: string,
        V_CHECK: string,
        V_PASSDESC: string
    }


    let auditNoResult: adudioNo = reactive({
        V_AUDIT_NO: '',
        V_CHECK: '',
        V_PASSDESC: ''
    })
    const cell: Array<any> = reactive([])
    let endBind = ref<boolean>(true)



    onMounted(() => {
        searchRef.value && searchRef.value.focus()
    })

    //查询
    function onSearch(val: any) {
        val = val.trim()
        //两种总包类型
        let pattern1 = /^[0-9]{30}$/
        let pattern2 = /^[A-Z]{15}[0-9]{14}$/

        if (!pattern1.test(val) && !pattern2.test(val)) {
            showFailToast('条码不符合规则');
        } else {
            //重置审核结果
            for (let key in auditNoResult) {
                auditNoResult[key] = ''
            }

            //清空数组
            cell.length = 0
            let bagNo = searchValue.value
            //数据库查序列号
            getAuditNo().then((res: any) => {
                if (res.data.length == 0) {
                    showFailToast('该邮袋未出库申请')
                } else {
                    let data = res.data[0]
                    auditNoResult.V_AUDIT_NO = data.V_AUDIT_NO
                    getAuditResult()
                    getAuditNoBagNo(bagNo)
                }
            }).catch((err: any) => {
                showNotify('出现错误，原因：' + err)
                console.log(err);
            })
        }
        searchValue.value = ''
        searchRef.value && searchRef.value.focus()
        closeToast();

    }


    //根据邮件查序列号
    function getAuditNo() {
        return axios_gh('/gjydhcxckxlh', {
            V_BAGNO: searchValue.value
        })
    }

    //海关-查询序列号审核结果
    function getAuditResult() {
        axios_hg({
            service: 'RetpostMailService',
            method: 'outbandAuditResultReport',
            params: auditNoResult.V_AUDIT_NO
        }).then((res: any) => {
            let data = res.result.info[0]
            if (data.flag == '0') {
                auditNoResult.V_CHECK = ''
                auditNoResult.V_PASSDESC = data.desc
                showButton.value = false
            } else {
                if (data.check == '1') {
                    auditNoResult.V_CHECK = '审核通过'
                    showButton.value = true
                } else if (data.check == '-1') {
                    auditNoResult.V_CHECK = '未进行审核'
                    auditNoResult.V_PASSDESC = data.passdesc
                    showButton.value = false
                } else {
                    auditNoResult.V_CHECK = '审核失败'
                    auditNoResult.V_PASSDESC = data.passdesc || data.desc
                    showButton.value = false
                }
                //更新数据库 updateauditno
                updateAuditNo(data.check)
            }
        }).catch((err: any) => {
            console.log(err)
            showNotify({ message: '(海关接口)审核结果查询失败,原因：' + err })
            auditNoResult.V_CHECK = '查询失败'
            auditNoResult.V_PASSDESC = '查询失败'
        })

    }

    //出库申请审核结果更新
    function updateAuditNo(check: string) {
        let new_params = {
            V_AUDIT_NO: auditNoResult.V_AUDIT_NO,
            V_CHECK: check,
            V_PASSDESC: auditNoResult.V_PASSDESC
        }
        axios_gh('/updateauditno', new_params).then((res: any) => {
            console.log(res)

        }).catch((err: any) => {
            console.log(err)
        })
    }

    //查询同序列号邮袋
    function getAuditNoBagNo(bagno: string) {
        axios_gh('/gjydhcxtcydqd', {
            V_BAGNO: bagno
        }).then((res: any) => {
            res.data.forEach((item: any) => {
                let object = { V_BAGNO: '', V_CHECK: '', V_PASSDESC: '' }
                object.V_BAGNO = item.V_BAGNO
                object.V_CHECK = item.V_CHECK
                cell.push(object)
            })
        }).catch((err: any) => {
            console.log(err)
            showNotify({ message: '查询失败' })
        })
        searchValue.value = ''
        searchRef.value && searchRef.value.focus()
    }

    //一键绑定
    // function onSubmit() {
    //     if (cell.length <= 0) {
    //         showFailToast({
    //             duration: 1000,
    //             message: '无数据绑定',
    //             forbidClick: true,
    //         })
    //     } else {
    //         showLoadingToast({
    //             duration: 0,
    //             message: '绑定中...',
    //             forbidClick: true,
    //         })
    //         cell.forEach((e: any) => {
    //             let params = []
    //             params.push(auditNoResult.V_AUDIT_NO)
    //             params.push(e.V_BAGNO)
    //             // params.push(homeStore.user.PERSON_NAME)
    //             params.push(Cookies.get("USER_NAME"))
    //             let new_params = (JSON.stringify(params)).replace(/\"/g, "'")
    //             outbandAuditBindReport(new_params).then((res: any) => {
    //                 let data = res.result.info[0]
    //                 if (data.flag == '0') {  //请求绑定失败
    //                     e.V_CHECK = '-1'
    //                     e.V_PASSDESC = data.desc

    //                 } else {
    //                     if (data.check == '1') {  //命中核查
    //                         e.V_CHECK = data.check
    //                     } else {
    //                         e.V_CHECK = data.check
    //                     }
    //                 }
    //                 //更新数据库
    //                 updateBag(Object.assign(toRaw(e), { V_AUDIT_NO: auditNoResult.V_AUDIT_NO }))
    //                 // inserterror(res.result.info[0], res.result.status, res.result.note)
    //             }).catch((err: any) => {
    //                 console.log(err)
    //                 showNotify({ message: e.V_BAGNO + '绑定失败' })
    //             })
    //         })
    //         endBind.value = false
    //         closeToast()
    //     }
    // }
    async function onSubmit() {
        if (cell.length <= 0) {
            showFailToast({
                duration: 1000,
                message: '无数据绑定',
                forbidClick: true,
            })
        } else {
            showLoadingToast({
                duration: 0,
                message: '绑定中...',
                forbidClick: true,
            })

            // 使用 Promise.all 等待所有绑定请求完成
            let promises = cell.map((e: any) => {
                let params = []
                params.push(auditNoResult.V_AUDIT_NO)
                params.push(e.V_BAGNO)
                params.push(Cookies.get("USER_NAME"))
                let new_params = JSON.stringify(params).replace(/\"/g, "'")

                return outbandAuditBindReport(new_params)
                    .then((res: any) => {
                        let data = res.result.info[0]
                        if (data.flag == '0') {
                            e.V_CHECK = '-1'
                            e.V_PASSDESC = data.desc
                        } else {
                            e.V_CHECK = data.check
                        }
                        // 更新数据库
                        return updateBag(Object.assign(toRaw(e), { V_AUDIT_NO: auditNoResult.V_AUDIT_NO }))
                    })
                    .catch((err: any) => {
                        console.log(err)
                        showNotify({ message: e.V_BAGNO + '绑定失败' })
                    })
            })

            // 等待所有 updateBag 操作完成后再继续
            Promise.all(promises).then(() => {
                endBind.value = false
                closeToast()
            }).catch((err: any) => {
                console.error("Error updating some bags:", err)
                closeToast()
            })
        }
    }


    //插入error表
    function inserterror(V_INFO: string | any[], V_STATUS: string, V_NOTE: string) {
        // 处理 V_INFO 是数组的情况
        let processedInfo = V_INFO;
        if (Array.isArray(V_INFO)) {
            processedInfo = V_INFO.map(item => {
                return `邮件号: ${item.mailNo}, 原因: ${item.mailRet}`;
            }).join('; ');
        }

        axios_gh('/errorresult',
            qs.stringify(
                {
                    V_INFO: processedInfo,
                    V_STATUS: V_STATUS,
                    V_NOTE: V_NOTE
                },
                { indices: false })
        ).then((res: any) => {
            console.log(res)
        }).catch((err: any) => {
            console.log(err)
            showNotify({ message: '出现错误， 原因：' + err })
        })
    }

    //海关-装车出库申请绑定接口
    async function outbandAuditBindReport(params: string) {
        //装车出库申请绑定接口
        return await axios_hg({
            service: 'RetpostMailService',
            method: 'outbandAuditBindReport',
            params: params
        })
    }

    //海关-装车出库申请绑定结束接口
    function outbandAuditEndBindReport() {
        // let params = [auditNoResult.V_AUDIT_NO, homeStore.user.PERSON_NAME]
        let params = [auditNoResult.V_AUDIT_NO, "admin"]
        let new_params = (JSON.stringify(params)).replace(/\"/g, "'")
        axios_hg({
            service: 'RetpostMailService',
            method: 'outbandAuditEndBindReport',
            params: new_params
        }).then((res: any) => {
            let data = res.result.info[0]
            if (data.flag == 1) {  //补录的核查命中邮袋号列表
                data.mailbags.forEach((e: string) => {
                    let object = {
                        V_BAGNO: e,
                        V_CHECK: 1,
                        V_PASSDESC: '',
                        V_AUDIT_NO: auditNoResult.V_AUDIT_NO
                    }
                    updateBag(object)
                })
                endBind.value = true
                showButton.value = false
                showToast('(海关接口)绑定结束成功')
            } else {
                showNotify('(海关接口)绑定结束失败')
            }

        }).catch((err: any) => {
            console.log(err)
            showNotify('(海关接口)出现错误， 原因：' + err)
        })
    }


    //更新数据库
    function updateBag(params: object) {
        axios_gh('/updatebag', params).then((res: any) => {
            console.log(res)
        }).catch((err: any) => {
            console.log(err)
        })
    }



    return {
        searchValue,
        searchRef,
        auditNoResult,
        cell,
        endBind,
        showButton,
        onSubmit,
        outbandAuditEndBindReport,
        onSearch
    };
}