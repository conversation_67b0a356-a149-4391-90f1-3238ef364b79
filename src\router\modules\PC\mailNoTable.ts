import { flag } from "@/router/utils";

export default {
  path: "/mailNoTable",
  redirect: "/mailNoTable/index",
  // component: () => import("@/views/PC/mailNoTable/index.vue"),
  meta: {
    icon: "ri:inbox-2-fill",
    title: "入库邮件表",
    rank: 1,
  },
  children: [
    {
      path: "/mailNoTable/index",
      name: "mailNoTable",
      component: () => import("@/views/PC/mailNoTable/index.vue"),
      meta: {
        showLink: flag ? false : true,
        title: "入库邮件表",
      }
    }
  ]
} satisfies RouteConfigsTable;
