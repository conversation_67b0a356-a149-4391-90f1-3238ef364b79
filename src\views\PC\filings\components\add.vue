<script setup lang="ts">
import { ref } from "vue";
import { useRouter } from "vue-router";
import "plus-pro-components/es/components/search/style/css";
import { type PlusColumn, type FieldValues, PlusForm } from "plus-pro-components";
import leftLine from "@iconify-icons/ri/arrow-left-s-line";
import { message } from "@/utils/message";
import { axios_gh, axios_hg } from "@/api/user";
import Cookies from "js-cookie";

const router = useRouter();

let form_columns: PlusColumn[] = [
    {
        label: "车牌号",
        prop: "V_CARNO",
        // tooltip: "显示6个字符",
        fieldProps: {
            placeholder: "请输入车牌号",
        }
    },
    {
        label: "类型",
        prop: "V_ENT_TYPE",
        valueType: "select",
        fieldProps: {
            placeholder: "全部"
        },
        options: [
            // {
            //     label: "全部",
            //     value: "",
            // },
            {
                label: "转场",
                value: "1",
            },
            {
                label: "转关",
                value: "2",
            },
            {
                label: "转运",
                value: "3",
            }
        ]
    },
    {
        label: "企业名称",
        prop: "V_ENT_NAME",
        fieldProps: {
            placeholder: "请输入企业名称",
        }
    },
    {
        label: "操作人",
        prop: "V_OPERNAME",
        fieldProps: {
            placeholder: "请输入操作人",
            disabled: true,
        },
    },
];

const rules = {
    V_CARNO: [
        {
            required: true,
            message: "请输入车牌号，至少7个字符",
            min: 7
        }
    ],
    V_ENT_TYPE: [
        {
            required: true,
            message: "请选择类型"
        }
    ],
    V_ENT_NAME: [
        {
            required: true,
            message: "请输入企业名称"
        }
    ]
};

let carInfo = ref<FieldValues>({
    V_CARNO: "",
    V_ENT_TYPE: "",
    V_ENT_NAME: "广航中心",
    V_OPERNAME: Cookies.get("USER_NAME")
});


// const handleChange = (values: FieldValues, prop: PlusColumn) => {
//     console.log(values, prop, "change");
// };

const handleReset = () => {
    // console.log("handleReset");
    message('重置成功', { type: 'success' })
};

const handleSubmit = (info: FieldValues) => {
    CarAddReport(info).then((res: any) => {
        let data = res.result.info[0]
        if (data.flag == '1') {
            message('(海关接口)新增成功', { type: 'success' })
            //插入数据库
            insertCarInfo(info)
            // router.push({ name: 'filings' })
        } else {
            message('(海关接口)确认失败，原因：' + data.desc, { type: 'error' })
        }

    }).catch((err: any) => {
        message('(海关接口)出现错误，原因：' + err, { type: 'error' })
    })
};



//海关-车辆信息备案录入
function CarAddReport(info: any) {
    let params = []
    for (let key in info) {
        params.push(info[key])
    }
    let new_params = (JSON.stringify(params)).replace(/\"/g, "'")

    return axios_hg({
        service: 'RetpostMailService',
        method: 'CarAddReport',
        params: new_params
    })
}

//插入数据库
function insertCarInfo(info: any) {
    axios_gh('/insertcarinfo', info).then((res: any) => {
        message('新增成功', { type: 'success' })
        carInfo = ref<FieldValues>({
            V_CARNO: "",
            V_ENT_TYPE: "",
            V_ENT_NAME: "广航中心",
            V_OPERNAME: Cookies.get("USER_NAME")
        });
    }).catch((err: any) => {
        message('新增失败', { type: 'error' })
    })
}

function onBackClick() {
    router.go(-1)
}



</script>

<template>
    <div class="addCar">
        <el-container>
            <el-main>
                <div class="flex items-center">
                    <el-button class="mb-[20px] float-right" @click="onBackClick">
                        <IconifyIconOffline :icon="leftLine" />返回
                    </el-button>
                </div>
                <PlusForm v-model="carInfo" class="w-[450px] m-auto" :columns="form_columns" :rules="rules"
                    label-position="right" @submit="handleSubmit" submit-text="新增" @reset="handleReset" reset-text="重置"
                    label-width="100">
                </PlusForm>
            </el-main>
        </el-container>
    </div>
</template>