<script setup lang="ts">
import { clone, delay } from "@pureadmin/utils";
import { ref, onMounted, reactive, watchEffect, toRaw } from "vue";
import { utils, writeFile } from "xlsx";
import { message } from "@/utils/message";
import { axios_gh } from "@/api/user";
import "plus-pro-components/es/components/search/style/css";
import { type PlusColumn, PlusSearch } from "plus-pro-components";
import { useColumns } from "./index";

const {
    columns,
    search_columns,
    auditno_select,
    pagination,
    dataList,
    loading,
    tableSize,
    handleChange,
    handleSearch,
    handleRest,
    onSizeChange,
    onCurrentChange,
    handleSelectionChange,
    exportExcel,
    getAuditNoData,
    onExamineResultClick,
    onDeleteClick,
    onUpdateClick,
    onConfirmOutboundClick,
    onClearBagClick
} = useColumns();

onMounted(() => {
    getAuditNoData(auditno_select.value).then((res: any) => {
        dataList.value = res;
        pagination.total = dataList.value.length;
        loading.value = false;
    });
})

</script>

<template>
    <div>
        <PlusSearch v-model="auditno_select" :columns="search_columns" :show-number="10" label-width="100"
            :col-props="{ span: 6 }" label-position="right" @change="handleChange" @search="handleSearch" search-text="查询"
            @reset="handleRest" reset-text="重置" />
        <br />
        <div class="float-left mb-[20px]">
            <el-button @click="onExamineResultClick">审核查询</el-button>
            <el-button type="primary" @click="onConfirmOutboundClick">出库确认</el-button>
        </div>
        <el-button type="primary" class="mb-[20px] float-right" @click="exportExcel">导出</el-button>
        <pure-table :data="dataList.slice(
            (pagination.currentPage - 1) * pagination.pageSize,
            pagination.currentPage * pagination.pageSize
        )" :columns="columns" alignWhole="center" border :height="tableSize === 'small' ? 352 : 460"
            :pagination="pagination" ref="waterRef" @page-size-change="onSizeChange"
            @page-current-change="onCurrentChange" @selection-change="handleSelectionChange">
            <template #operation="{ row }">
                <el-button link type="primary" size="small" @click="onUpdateClick(row)">
                    修改
                </el-button>
                <el-popconfirm title="确定要清空总包号吗?" @confirm="onClearBagClick(row)">
                    <template #reference>
                        <el-button link type="warning" size="small">
                            清空
                        </el-button>
                    </template>
                </el-popconfirm>
                <el-popconfirm v-if="row.V_CHECK != 1" title="你确定删除?" @confirm="onDeleteClick(row)">
                    <template #reference>
                        <el-button link type="danger" size="small">
                            删除
                        </el-button>
                    </template>
                </el-popconfirm>
            </template>
        </pure-table>
    </div>
</template>
