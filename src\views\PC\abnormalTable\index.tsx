import { clone, delay } from "@pureadmin/utils";
import { ref, onMounted, reactive, watchEffect, toRaw } from "vue";
import type { PaginationProps, AdaptiveConfig, LoadingConfig, Align } from "@pureadmin/table";
import { utils, writeFile } from "xlsx";
import { showNotify, showFailToast, showToast, showDialog, showConfirmDialog } from 'vant';
import { message } from "@/utils/message";
import { axios_gh, axios_hg } from "@/api/user";
import "plus-pro-components/es/components/search/style/css";
import { type PlusColumn, PlusSearch } from "plus-pro-components";
export function useColumns() {

    let columns: TableColumnList = [
        {
            type: "selection",
            align: "left",
            fixed: true
        },
        {
            label: "异常情况编号",
            prop: "V_ABNO",
            width: 300,
            fixed: true
        },
        {
            label: "企业名称",
            prop: "V_ENTNAME",
            width: 170,
            showOverflowTooltip: true,
        },
        {
            label: "邮件号",
            prop: "V_MAILNO",
            width: 160
        },
        {
            label: '处置类型',
            prop: 'V_ABNORMALTYPE',
            width: 200,
            cellRenderer: ({ row }) => {
                switch (row.V_ABNORMALTYPE) {
                    case '1':
                        return '退运已入库转其他原因出库';
                    case '2':
                        return '退运已装袋转退运已入库';
                    case '3':
                        return '退运已出库转退运已入库';
                    default:
                        return '';
                }

            }
        },
        {
            label: '相关情况说明',
            prop: 'V_ABINFO',
            width: 140
        },
        {
            label: '指令状态',
            prop: 'V_INSTRSTATE',
            width: 140
        },
        {
            label: '文件名称',
            prop: 'V_FILENAME',
            width: 160,
            showOverflowTooltip: true,
        },
        {
            label: '申报人',
            prop: 'V_OPERATORNAME',
            width: 120
        },
        {
            label: '申报时间',
            prop: 'D_OPER_DATE',
            width: 120
        },
        {
            label: '审核意见',
            prop: 'V_MAILRET',
            width: 180
        },
        {
            label: "操作",
            width: "120",
            fixed: "right",
            slot: "operation"
        }

    ];

    let search_columns: PlusColumn[] = [
        {
            label: "邮件号",
            prop: "V_MAILNO",
            // tooltip: "显示6个字符",
            fieldProps: {
                placeholder: "请输入邮件号"
            }
        },
        {
            label: "处置类型",
            prop: "V_ABNORMALTYPE",
            valueType: "select",
            fieldProps: {
                placeholder: "全部"
            },
            options: [
                {
                    label: "全部",
                    value: "",
                },
                {
                    label: "退运已入库转其他原因出库",
                    value: "1",
                },
                {
                    label: "退运已装袋转退运已入库",
                    value: "2",
                },
                {
                    label: "退运已出库转退运已入库",
                    value: "3",
                }
            ]
        },
        {
            label: "申报时间",
            prop: "D_OPER_DATE",
            valueType: "date-picker",
            fieldProps: {
                type: "daterange",
                startPlaceholder: "开始时间",
                endPlaceholder: "结束时间",
                valueFormat: "YYYY/MM/DD"
            }
        },
        {
            label: "申报人",
            prop: "V_OPERATORNAME",
            fieldProps: {
                placeholder: "请输入申报人"
            }
        },
    ];

    const dataList = ref([]);
    const loading = ref(true);
    const tableSize = ref("default");
    let multipleSelection = ref([]);
    // let tableData = [];

    //默认查询数据
    let auditno_select = ref({
        V_AUDIT_NO: '',
        D_OPER_DATE: ['', ''],
        V_OPERNAME: '',
        V_BAGNO: '',
    });



    /** 分页配置 */
    const pagination = reactive<PaginationProps>({
        pageSize: 10,
        currentPage: 1,
        pageSizes: [10, 50, 200, 500, 1000],
        total: 0,
        align: "center",
        background: true,
        small: false
    });


    /** 加载动画配置 */
    const loadingConfig = reactive<LoadingConfig>({
        text: "正在加载第一页...",
        viewBox: "-10, -10, 50, 50",
        spinner: `
    <path class="path" d="
        M 30 15
        L 28 17
        M 25.61 25.61
        A 15 15, 0, 0, 1, 15 30
        A 15 15, 0, 1, 1, 27.99 7.5
        L 15 15
    " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>
    `
        // svg: "",
        // background: rgba()
    });

    const handleChange = (values: any) => {
        console.log(values, "change");
    };
    //查询
    const handleSearch = (values: any) => {
        const searchParams = {
            ...values,
            D_OPER_DATE_START: values.D_OPER_DATE?.[0] || '',
            D_OPER_DATE_END: values.D_OPER_DATE?.[1] || '',
        };

        // 删除原始日期范围字段
        delete searchParams.D_OPER_DATE;

        getAuditNoData(toRaw(searchParams)).then((res) => {
            dataList.value = res;
            pagination.total = dataList.value.length;
            loading.value = false;
            message('查询成功', { type: 'success' })
        });
    };

    function onSizeChange(val: number) {
        message('更改为每页' + val + '条', { type: 'success', offset: 500 });
    }

    function onCurrentChange(val: number) {
        // console.log(val);

        // loadingConfig.text = `正在加载第${val}页...`;
        // loading.value = true;
        // delay(600).then(() => {
        //     loading.value = false;
        // });
    }

    const handleSelectionChange = (val: any) => {
        multipleSelection.value = val;
    };

    // 重置
    const handleRest = () => {
        auditno_select = ref({
            V_AUDIT_NO: '',
            D_OPER_DATE: ['', ''],
            V_OPERNAME: '',
            V_BAGNO: '',
        });
        handleSearch(auditno_select.value)
    };

    const exportExcel = () => {
        const res = dataList.value.map(item => {
            const arr = [];
            columns.forEach((column, index) => {
                if (index > 0) {
                    arr.push(item[column.prop as string]);
                }
            });
            return arr;
        });
        const titleList = [];
        columns.forEach((column, index) => {
            if (index > 0) {
                titleList.push(column.label);
            }
        });
        res.unshift(titleList);
        const workSheet = utils.aoa_to_sheet(res);
        //设置列宽
        workSheet['!cols'] = [
            { wpx: 180 },
            { wpx: 180 },
            { wpx: 100 },
            { wpx: 80 },
            { wpx: 80 },
            { wpx: 80 },
            { wpx: 180 },
            { wpx: 80 },
            { wpx: 100 },
            { wpx: 180 },
        ]
        const workBook = utils.book_new();
        utils.book_append_sheet(workBook, workSheet, "异常邮件");
        writeFile(workBook, "异常邮件表" + new Date().getTime() + ".xlsx");
        message("导出成功", {
            type: "success"
        });
    };

    //获取邮袋数据
    const getAuditNoData = (object?: any) => {
        return axios_gh('/cxycyj', object).then((res: any) => {
            let data = res.data
            for (let i = 0; i < data.length; i++) {
                data[i]["key"] = i
            }
            // tableData = []
            // tableData = data
            return data
            // message('查询成功', { type: 'success' })
        })
    }
    /**
     * 审核意见
     */
    //审核查询
    function onExamineClick() {
        let data_array = toRaw(multipleSelection.value);
        let len = data_array.length;
        if (len > 0) {
            for (let i = 0; i < len; i++) {
                let mailno = data_array[i].V_MAILNO
                mailAbnormalDisposeQueryReport(mailno).then((res: any) => {
                    let data = res.result.info[0]
                    let mailNo = data.mailNo
                    let mailRet = data.mailRet
                    if (res.succeeded == true) {
                        //更新查验结果
                        updateAbnormalMailRet(mailNo, mailRet)
                        message(mailRet, { type: 'success' })
                    } else {
                        message(mailRet, { type: 'error' })
                    }
                })
            }
        } else {
            message('请选择数据', { type: 'error' })
        }
    }

    //海关-异常邮件审核意见查询
    function mailAbnormalDisposeQueryReport(mailno: string) {
        let params = [mailno]
        let new_params = (JSON.stringify(params)).replace(/\"/g, "'")
        return axios_hg({
            service: 'RetpostMailService',
            method: 'mailAbnormalDisposeQueryReport',
            params: new_params
        })
    }


    //更新异常邮件审核意见
    function updateAbnormalMailRet(mailNo: string, mailRet: string) {
        return axios_gh('/updateabnormalmailret', {
            V_MAILNO: mailNo,
            V_MAILRET: mailRet
        })
    }


    /**
     *  删除
     */

    //删除
    function onDeleteClick(row: any) {
        let mailno = row.V_MAILNO
        let abno = row.V_ABNO
        hgDelete(mailno, abno).then((res: any) => {
            let data = res.result.info[0]
            let dataArray = data.mailRet.split('-')
            if (dataArray[0] == 'success') {
                message(dataArray[1], { type: 'success' })
                deleteAbnormal(mailno, abno)
            } else {
                message(dataArray[1], { type: 'error' })
            }
        }).catch((err) => {
            console.log(err);
            message('(海关)删除失败', { type: 'error' })
        })
    }

    //海关删除接口
    function hgDelete(mailNo: string, abNo: string) {
        let params = [mailNo, abNo]
        let new_params = (JSON.stringify(params)).replace(/\"/g, "'")
        return axios_hg({
            service: 'RetpostMailService',
            method: 'mailAbnormalDisposeDelReport',
            params: new_params
        })
    }

    //删除异常邮件
    function deleteAbnormal(mailNo: string, abNo: string) {
        axios_gh('/deleteabnoraml', {
            V_MAILNO: mailNo,
            V_ABNO: abNo
        }).then(res => {
            getAuditNoData(auditno_select.value).then((res) => {
                dataList.value = res;
                pagination.total = dataList.value.length;
                loading.value = false;
            });
        }).catch(err => {
            console.log(err);
            message('删除失败', { type: 'error' })
        })
    }


    return {
        columns,
        search_columns,
        auditno_select,
        pagination,
        loadingConfig,
        dataList,
        loading,
        tableSize,
        // tableData,
        handleChange,
        handleSearch,
        handleRest,
        onSizeChange,
        onCurrentChange,
        handleSelectionChange,
        exportExcel,
        getAuditNoData,
        onDeleteClick,
        onExamineClick
    };
}
