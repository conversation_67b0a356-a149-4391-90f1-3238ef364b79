import { flag } from "@/router/utils";
export default {
    path: "/userTable",
    redirect: "/userTable/index",
    meta: {
        icon: "ri:group-fill",
        title: "用户管理表",
        rank: 5
    },
    children: [
        {
            path: "/userTable/index",
            name: "userTable",
            component: () => import("@/views/PC/userTable/index.vue"),
            meta: {
                showLink: flag ? false : true,
                title: "用户管理表",
            }
        },
        {
            path: "/userTable/add",
            name: "addUser",
            component: () => import("@/views/PC/userTable/components/add.vue"),
            meta: {
                showLink: false,
                title: "新增用户",
            }
        }
    ]
} satisfies RouteConfigsTable;