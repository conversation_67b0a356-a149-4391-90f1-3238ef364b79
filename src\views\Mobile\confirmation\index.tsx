import qs from 'qs'
import { axios_gh, axios_hg } from "@/api/user";
import { ref, reactive, onMounted, toRaw, getCurrentInstance } from 'vue'
import { showToast, showFailToast, showNotify, showLoadingToast, closeToast, showDialog } from 'vant'
import Cookies from 'js-cookie';

export function useColumns() {
    let searchValue = ref<string>()
    let searchRef = ref<HTMLInputElement | null>(null)
    let V_AUDIT_TYPE = ref<string>()
    let show = ref<boolean>(true)


    interface form {
        V_AUDIT_NO: string,
        V_LOCK_NO: string
        V_DRIVER_NO: string
        V_OPERNAME: string
    }

    let formData: any = reactive({
        V_AUDIT_NO: '',
        V_LOCK_NO: '',
        V_DRIVER_NO: '',
        // V_OPERNAME: homeStore.user.PERSON_NAME
        V_OPERNAME: Cookies.get("USER_NAME")
    })


    onMounted(() => {
        searchRef.value && searchRef.value.focus()
    })


    function onSubmit() {
        outbandLiveReport()
    }

    //装车确认接口
    function outbandLiveReport() {
        let params = []
        for (let key in formData) {
            params.push(formData[key])
        }
        let new_params = (JSON.stringify(params)).replace(/\"/g, "'")

        axios_hg({
            service: 'RetpostMailService',
            method: 'outbandLiveReport',
            params: new_params
        }).then((res: any) => {
            let data = res.result.info[0]
            if (data.flag == '1') {
                showToast('(海关接口)确认成功')
            } else {
                showNotify('(海关接口)确认失败， 原因：' + data.desc)
                inserterror(res.result.info, res.result.status, res.result.note)
            }
            updateflag(data.flag)
        }).catch((err: any) => {
            console.log(err);
            showNotify('(海关接口)出现错误， 原因：' + err)
            inserterror(err.result.info, err.result.status, err.result.note)
        })
    }

    function inserterror(V_INFO: string | any[], V_STATUS: string, V_NOTE: string) {
        // 处理 V_INFO 是数组的情况
        let processedInfo = V_INFO;
        if (Array.isArray(V_INFO)) {
            processedInfo = V_INFO.map(item => {
                return `邮件号: ${item.mailNo}, 原因: ${item.mailRet}`;
            }).join('; ');
        }

        axios_gh('/errorresult',
            qs.stringify(
                {
                    V_INFO: processedInfo,
                    V_STATUS: V_STATUS,
                    V_NOTE: V_NOTE
                },
                { indices: false })
        ).then((res: any) => {
            console.log(res)
        }).catch((err: any) => {
            console.log(err)
            showNotify({ message: '出现错误， 原因：' + err })
        })
    }
    //插入数据
    function updateflag(flag: string) {
        let newObject = { ...formData }
        Object.assign(newObject, { V_FLAG: flag })
        axios_gh('/updateflag',
            toRaw(newObject)
        ).then((res: any) => {
            console.log(res);
        }).catch((err: any) => {
            console.log(err);
            showNotify('出现错误， 原因：' + err)
        })
    }


    //扫描邮袋号
    function onSearch(val: any) {
        val = val.trim()
        //两种总包类型
        let pattern1 = /^[0-9]{30}$/
        let pattern2 = /^[A-Z]{15}[0-9]{14}$/

        if (!pattern1.test(val) && !pattern2.test(val)) {
            showFailToast('条码不符合规则');
        } else {
            //获取序列号
            axios_gh('/gjydhcxckxlh', {
                V_BAGNO: searchValue.value
            }).then((res: any) => {

                if (res.data.length == 0) {
                    showFailToast('该邮袋未出库申请')
                } else {
                    let data = res.data[0];
                    formData.V_AUDIT_NO = data.V_AUDIT_NO

                    //判断类型
                    if (data.V_AUDIT_TYPE == '1') {
                        V_AUDIT_TYPE.value = '转场'
                    } else if (data.V_AUDIT_TYPE == '2') {
                        V_AUDIT_TYPE.value = '转关'
                    } else {
                        V_AUDIT_TYPE.value = '转运'
                        show.value = false
                    }
                }

            }).catch((err: any) => {
                console.log(err);
                showNotify('出现错误， 原因：' + err)
            })
        }
        searchValue.value = ''
        searchRef.value && searchRef.value.focus()
    }
    return {
        searchValue,
        searchRef,
        V_AUDIT_TYPE,
        formData,
        show,
        onSearch,
        onSubmit
    };
}