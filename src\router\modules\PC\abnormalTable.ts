import { flag } from "@/router/utils";
export default {
    path: "/abnormalTable",
    redirect: "/abnormalTable/index",
    meta: {
        icon: "ri:file-close-fill",
        title: "异常邮件表",
        rank: 4
    },
    children: [
        {
            path: "/abnormalTable/index",
            name: "abnormalTable",
            component: () => import("@/views/PC/abnormalTable/index.vue"),
            meta: {
                showLink: flag ? false : true,
                title: "异常邮件表"
            }
        }
    ]
} satisfies RouteConfigsTable;
