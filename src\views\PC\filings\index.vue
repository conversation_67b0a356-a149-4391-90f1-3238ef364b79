<script setup lang="ts">
import { onMounted, ref } from "vue";
import "plus-pro-components/es/components/search/style/css";
import { type PlusColumn, PlusSearch } from "plus-pro-components";
import { useColumns } from "./index";

const {
    columns,
    search_columns,
    filings_select,
    pagination,
    dataList,
    loading,
    tableSize,
    handleChange,
    handleSearch,
    handleRest,
    onSizeChange,
    onCurrentChange,
    handleSelectionChange,
    exportExcel,
    getCarListTable,
    onAddClick,
    onDeleteClick,
    onCheckClik,
    onUpdateClick
} = useColumns();


onMounted(() => {
    getCarListTable(filings_select).then((res: any) => {
        dataList.value = res;
        pagination.total = dataList.value.length;
        loading.value = false;
    })
});




</script>

<template>
    <div>
        <PlusSearch v-model="filings_select" :columns="search_columns" :show-number="10" label-width="80"
            :col-props="{ span: 6 }" label-position="right" @change="handleChange" @search="handleSearch"
            search-text="查询" @reset="handleRest" reset-text="重置" />
        <br />
        <el-button class="float-left mb-[20px]" @click="onCheckClik">审核情况</el-button>
        <div class="float-right mb-[20px]">
            <el-button @click="onAddClick">新增</el-button>
            <el-button type="primary" @click="exportExcel">导出</el-button>
        </div>
        <pure-table :data="dataList.slice(
            (pagination.currentPage - 1) * pagination.pageSize,
            pagination.currentPage * pagination.pageSize
        )" :columns="columns" border alignWhole="center" :height="tableSize === 'small' ? 352 : 460"
            :pagination="pagination" ref="waterRef" @page-size-change="onSizeChange"
            @page-current-change="onCurrentChange" @selection-change="handleSelectionChange">
            <template #operation="{ row }">
                <el-button link type="primary" size="small" @click="onUpdateClick(row)">
                    修改
                </el-button>
                <el-popconfirm title="你确定删除?" @confirm="onDeleteClick(row)">
                    <template #reference>
                        <el-button link type="danger" size="small">
                            删除
                        </el-button>
                    </template>
                </el-popconfirm>
            </template>
        </pure-table>
    </div>
</template>