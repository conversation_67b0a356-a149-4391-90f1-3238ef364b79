<script setup lang="ts">
import { ref } from "vue";
import leftLine from "@iconify-icons/ri/arrow-left-s-line";
import { type PlusColumn, type FieldValues, PlusForm } from "plus-pro-components";
import { useRouter, useRoute } from "vue-router";
import { message } from "@/utils/message";
import Cookies from "js-cookie";
import { axios_hg, axios_gh } from "@/api/user";

const router = useRouter();
const route = useRoute();


let form_columns: PlusColumn[] = [
    {
        label: "车牌号",
        prop: "V_CARNO",
        // tooltip: "显示6个字符",
        fieldProps: {
            placeholder: "请输入车牌号"
        }
    },
    {
        label: "类型",
        prop: "V_ENT_TYPE",
        valueType: "select",
        fieldProps: {
            placeholder: "全部"
        },
        options: [
            {
                label: "全部",
                value: "",
            },
            {
                label: "转场",
                value: "1",
            },
            {
                label: "转关",
                value: "2",
            },
            {
                label: "转运",
                value: "3",
            }
        ]
    },
    {
        label: "企业名称",
        prop: "V_ENT_NAME",
        // tooltip: "显示6个字符",
        fieldProps: {
            placeholder: "请输入企业名称"
        }
    },
    {
        label: "操作人",
        prop: "V_OPERNAME",
        // tooltip: "显示6个字符",
        fieldProps: {
            placeholder: "请输入操作人",
            disabled: true,
        },
    },
];

const rules = {
    V_CARNO: [
        {
            required: true,
            message: "请输入车牌号"
        }
    ],
    V_ENT_TYPE: [
        {
            required: true,
            message: "请选择类型"
        }
    ],
    V_ENT_NAME: [
        {
            required: true,
            message: "请输入企业名称"
        }
    ]
};

let carInfo = ref<FieldValues>({
    V_CARNO: route.query.carno,
    V_ENT_TYPE: route.query.type,
    V_ENT_NAME: "广航中心",
    V_OPERNAME: Cookies.get("USER_NAME")
});

const handleReset = () => {
    // console.log("handleReset");
    message('重置成功', { type: 'success' })
};

const handleSubmit = (info: any) => {
    carUpdateReport(info.V_CARNO, info.V_ENT_TYPE, info.V_ENT_NAME).then((res: any) => {
        let data = res.result.info[0]
        if (data.flag == '1') {
            message('(海关接口)修改成功', { type: 'success' })
            updateCarInfo(info)
        } else {
            message('(海关接口)修改失败', { type: 'error' })
        }
    }).catch((err: any) => {
        console.log(err);
        message('(海关接口)出现错误, 原因：' + err, { type: 'error' })
    })

};

//海关-车辆信息备案修改
function carUpdateReport(carNo: string, entType: string, entName: string) {
    let params = [Cookies.get("USER_NAME")]
    params.unshift(entName)
    params.unshift(entType)
    params.unshift(carNo)
    let new_params = (JSON.stringify(params)).replace(/\"/g, "'")
    return axios_hg({
        service: 'RetpostMailService',
        method: 'CarUpdateReport',
        params: new_params
    })
}

//修改车辆信息
function updateCarInfo(info: any) {
    axios_gh('/clxxxg', info).then((res: any) => {
        console.log(res);
    }).catch((err: any) => {
        console.log(err);
    })
}


function onBackClick() {
    router.go(-1)
}

</script>



<template>
    <div class="updateCar">
        <el-container>
            <el-main>
                <div class="flex items-center">
                    <el-button class="mb-[20px] float-right" @click="onBackClick">
                        <IconifyIconOffline :icon="leftLine" />返回
                    </el-button>
                </div>
                <PlusForm v-model="carInfo" class="w-[450px] m-auto" :columns="form_columns" :rules="rules"
                    label-position="right" @submit="handleSubmit" submit-text="修改" @reset="handleReset" reset-text="重置"
                    label-width="100">
                </PlusForm>
            </el-main>
        </el-container>
    </div>
</template>