<script setup lang="ts">
import { clone, delay } from "@pureadmin/utils";
import { ref, onMounted, reactive, watchEffect, toRaw } from "vue";
import { utils, writeFile } from "xlsx";
import { message } from "@/utils/message";
import { axios_gh } from "@/api/user";
import "plus-pro-components/es/components/search/style/css";
import { type PlusColumn, PlusSearch } from "plus-pro-components";
import { useColumns } from "./index";

const {
    columns,
    search_columns,
    user_select,
    pagination,
    dataList,
    loading,
    tableSize,
    handleChange,
    handleSearch,
    handleRest,
    onSizeChange,
    onCurrentChange,
    handleSelectionChange,
    exportExcel,
    getUserData,
    onAddClick,
    onResetPswClick,
    onDeleteClick
} = useColumns();

onMounted(() => {
    getUserData(user_select.value).then((res: any) => {
        dataList.value = res;
        pagination.total = dataList.value.length;
        loading.value = false;
    });
})

</script>

<template>
    <div>
        <PlusSearch v-model="user_select" :columns="search_columns" :show-number="10" label-width="60" input-width="100"
            :col-props="{ span: 6 }" label-position="right" @change="handleChange" @search="handleSearch"
            search-text="查询" @reset="handleRest" reset-text="重置" />
        <br />
        <div class="mb-[20px] float-right">
            <el-button @click="onAddClick">新增</el-button>
            <el-button type="primary" @click="exportExcel">导出</el-button>
        </div>

        <pure-table :data="dataList.slice(
            (pagination.currentPage - 1) * pagination.pageSize,
            pagination.currentPage * pagination.pageSize
        )" :columns="columns" alignWhole="center" border :height="tableSize === 'small' ? 352 : 460"
            :pagination="pagination" ref="waterRef" @page-size-change="onSizeChange"
            @page-current-change="onCurrentChange" @selection-change="handleSelectionChange">
            <template #operation="{ row }">
                <el-button link type="primary" size="small" @click="onResetPswClick(row)">
                    重置密码
                </el-button>
                <el-popconfirm v-if="row.V_AUDIT_NO == null" title="你确定删除?" @confirm="onDeleteClick(row)">
                    <template #reference>
                        <el-button link type="danger" size="small">
                            删除
                        </el-button>
                    </template>
                </el-popconfirm>
            </template>
        </pure-table>

    </div>
</template>