<script setup lang="ts">
import "plus-pro-components/es/components/search/style/css";
import { reactive, toRaw, ref, onMounted, getCurrentInstance } from "vue";
import { useColumns } from "./index";
import { getMine } from "@/api/user";

const {
    search,
    searchRef,
    showHgResult,
    warehousing_form,
    onSearch,
    mail_num,
    forceMode,
    toggleForceMode,
} = useColumns();

onMounted(() => {
    searchRef.value && searchRef.value.focus();
})

// getMine().then(res => {
//     console.log(res.data);
    
// });

</script>

<template>
    <div class="Warehousing">
        <header>
            <!-- <van-search v-model="search.value" shape="round" background="#1989fa" placeholder="请输入邮件号"
                @search="onSearch" ref="searchRef" /> -->
            <van-search v-model="search.value" shape="round" background="#1989fa" placeholder="请输入邮件号"
                @search="onSearch" ref="searchRef">
                <template #right-icon>
                    <van-button size="small" :type="forceMode ? 'danger' : 'primary'" @click.stop="toggleForceMode">
                        {{ forceMode ? '强制模式' : '标准模式' }}
                    </van-button>
                </template>
            </van-search>
        </header>
        <main>
            <van-cell-group>
                <van-field v-model="warehousing_form.V_MAILNO" label="邮件号" readonly />
                <van-field label="邮件类型" readonly>
                    <template #input>
                        <div style="font-size: 20px; font-weight: bold;">{{ warehousing_form.MAILTYPE }}</div>
                    </template>
                </van-field>
                <van-field v-model="warehousing_form.D_RETURN_DATE" label="时间" readonly />
                <van-field v-model="mail_num" label="已扫数量" readonly />
                <van-field v-model="warehousing_form.V_OPERNAME" label="操作人" disabled />
            </van-cell-group>
            <van-cell-group v-show="showHgResult">
                <van-divider :style="{ color: '#1989fa', borderColor: '#1989fa', padding: '0 16px' }">
                    海关反馈信息
                </van-divider>
                <van-field v-model="warehousing_form.V_NOTE" label="反馈结果" readonly type="textarea" />
            </van-cell-group>
        </main>
    </div>
</template>
<style scoped>
/* 添加模式提示样式 */
.van-search__content {
    align-items: center;
}

.van-button {
    margin-left: 10px;
    transition: all 0.3s;
}
</style>