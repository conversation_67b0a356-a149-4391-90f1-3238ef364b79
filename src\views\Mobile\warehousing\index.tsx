import { ref, reactive, toRaw } from "vue";
import { axios_gh, axios_hg, axios_gh1 } from "@/api/user";
import "plus-pro-components/es/components/search/style/css";
import { type PlusColumn, PlusSearch } from "plus-pro-components";
import { showNotify, showFailToast, showToast, showDialog, showConfirmDialog } from 'vant';
import Cookies from 'js-cookie';
export function useColumns() {
    let searchRef = ref<HTMLInputElement | null>(null)
    let search = reactive({ value: '' })
    let showHgResult = ref<boolean>(false)
    let mail_num = ref<number>(0)

    function cellList() {
        return {
            V_MAILNO: '',
            MAILTYPE: '',
            D_RETURN_DATE: '',
            V_OPERNAME: Cookies.get("USER_NAME"),
            V_NOTE: '',
        }
    }
    let warehousing_form = reactive(cellList())


    //查询
    // function onSearch(val: string) {
    //     val = val.trim()
    //     Object.assign(warehousing_form, cellList())
    //     //邮件号规则
    //     let pattern = /^[A-Z]{2}[0-9]{9}[A-Z]{2}$/
    //     //判断是否符合规则
    //     if (pattern.test(val)) {
    //         warehousing_form.V_MAILNO = val
    //         //先判断截留情况
    //         judgeZl(val)
    //     } else {
    //         showFailToast('不符合邮件号规则');
    //     }
    //     searchRef.value && searchRef.value.focus()
    //     search.value = ''
    // }

    // useColumns.ts 修改部分
    const forceMode = ref(false) // 新增模式状态

    const toggleForceMode = () => { // 新增模式切换方法
        forceMode.value = !forceMode.value
    }

    function onSearch(val: string) {
        val = val.trim()
        Object.assign(warehousing_form, cellList())

        // 强制模式逻辑
        if (forceMode.value) {
            warehousing_form.V_MAILNO = val
            mainReport(val, warehousing_form.MAILTYPE || '1') // 使用已有类型或默认值
            searchRef.value && searchRef.value.focus()
            search.value = ''
            return
        }

        // 标准模式原有逻辑
        let pattern = /^[A-Z]{2}[0-9]{9}[A-Z]{2}$/
        if (pattern.test(val)) {
            warehousing_form.V_MAILNO = val
            judgeZl(val)
        } else {
            showFailToast('不符合邮件号规则')
        }

        searchRef.value && searchRef.value.focus()
        search.value = ''
    }

    //判断截留
    function judgeZl(mailNo: string) {
        let result = false
        axios_gh('/cxjlyj', {
            V_MAILNO: mailNo
        }).then((res: any) => {
            if (res.success == true) {
                if (res.data.length > 0) {
                    showHgResult.value = false
                    result = true
                    showToast({
                        message: '该邮件截留',
                        position: 'top'
                    })
                    warehousing_form.MAILTYPE = '截留'
                    warehousing_form.D_RETURN_DATE = res.data[0].D_DATE
                } else {  //不截留，判断本转关
                    judgeBz(mailNo)
                }
            }
        }).catch((err: any) => {
            console.log(err);
            showNotify('出现错误，原因：' + err)
        })
        return result
    }


    //判断本转关
    function judgeBz(mailNo: string) {
        axios_gh('/pdbz', {
            V_MAILNO: mailNo
        }).then((res: any) => {
            if (res.success == true) {
                if (res.data[0].NUM > 0) {  //转关
                    warehousing_form.MAILTYPE = '本关'
                    judgeTy(mailNo)
                } else {  //本关，判断是不是退运邮件

                    showToast({
                        message: '该邮件转关',
                        position: 'top'
                    })
                    warehousing_form.MAILTYPE = '转关'
                    mainReport(mailNo, '2')
                }
            } else {
                showNotify({ message: '查询失败' })
            }
        }).catch((err: any) => {
            console.log(err)
            showNotify('出现错误，原因：' + err)
        })
    }


    //判断退运
    function judgeTy(mailNo: string) {
        axios_gh('/pdty', {
            V_MAILNO: mailNo
        }).then((res: any) => {
            if (res.success == true) {
                if (res.data[0].TIME != null) { //退运给海关审查
                    warehousing_form.D_RETURN_DATE = res.data[0].TIME
                    warehousing_form.MAILTYPE = '本关兼退运'
                    //海关审查
                    mainReport(mailNo, '1')
                } else {
                    showToast({
                        message: '该邮件不是退运',
                        position: 'top'
                    })
                    warehousing_form.MAILTYPE = '本关非退运'
                }
            }
        }).catch((err: any) => {
            console.log(err)
            showNotify('出现错误，原因：' + err)
        })
    }

    //判断是否存在邮件号
    // async function judgeExist(mailNo: string) {
    //     await axios_gh('/cxyjxx', {
    //         V_MAILNO: mailNo
    //     }).then((res: any) => {
    //         if (res.data.length > 0) {
    //             return true
    //         } else {
    //             return false
    //         }
    //     }).catch((err: any) => {
    //         console.log(err)

    //     })
    //     return false
    // }

    async function judgeExist(mailNo: string): Promise<boolean> {
        try {
            const res = await axios_gh1('/cxyjxx', {
                V_MAILNO: mailNo
            });
            return res.data.length > 0;
        } catch (err) {
            console.log(err);
            return false;
        }
    }



    async function judgeExistbagno(mailNo: string): Promise<boolean> {
        try {
            const res = await axios_gh1('/cxyjxx', {
                V_MAILNO: mailNo
            });
            return res.data[0].V_BAGNO !== null;
        } catch (err) {
            console.log(err);
            return false;
        }
    }


    function inserterror(V_INFO: string | any[], V_STATUS: string, V_NOTE: string) {
        // 处理 V_INFO 是数组的情况
        let processedInfo = V_INFO;
        if (Array.isArray(V_INFO)) {
            processedInfo = V_INFO.map(item => {
                return `邮件号: ${item.mailNo}, 原因: ${item.mailRet}`;
            }).join('; ');
        }

        axios_gh('/errorresult',
            qs.stringify(
                {
                    V_INFO: processedInfo,
                    V_STATUS: V_STATUS,
                    V_NOTE: V_NOTE
                },
                { indices: false })
        ).then((res: any) => {
            console.log(res)
        }).catch((err: any) => {
            console.log(err)
            showNotify({ message: '出现错误， 原因：' + err })
        })
    }

    //海关-审查,入库申报接口
    function mainReport(mailNo: string, mailType: string) {
        showHgResult.value = true
        // let params = [homeStore.user.PERSON_NAME]
        let params = [Cookies.get("USER_NAME")]
        params.unshift(mailType)
        params.unshift(mailNo)
        let new_params = (JSON.stringify(params)).replace(/\"/g, "'")
        axios_hg(
            {
                service: 'RetpostMailService',
                method: 'mailReport',
                params: new_params
            }
        ).then((res: any) => {
            //     let mailRet = res.result.info[0].mailRet
            //     let retArray = mailRet.split('-')
            //     warehousing_form.V_NOTE = retArray[retArray.length - 1]
            //     if ('success' in retArray[0] ) {
            //         showToast(retArray[1])
            //         insert()
            //         // if(judgeExist(mailNo)){ //存在，更新数据库
            //         //     update()
            //         // }else {//插入数据
            //         //     insert()
            //         // }
            //     } else  if ("已入库" in retArray[2] ){
            //         if (!judgeExist(mailNo)) { //海关存在，我们没有的插入数据库

            //         //插入数据
            //             insert()
            //         }
            //         showNotify({ type: 'warning', message: retArray[2]})
            //     }
            // }).catch((err: any) => {
            //     console.log(err);
            //     warehousing_form.V_NOTE = '(海关接口)海关审查失败!!'
            // })
            if (res.result && res.result.info && res.result.info.length > 0) {
                let mailRet = res.result.info[0].mailRet;
                let retArray = mailRet.split('-');

                warehousing_form.V_NOTE = retArray[retArray.length - 1];

                if (retArray[0]?.includes('success')) {
                    showToast(retArray[1]);
                    insert();
                    // if (judgeExist(mailNo)) { // 存在，更新数据库
                    //     update();
                    // } else { // 插入数据
                    //     insert();
                    // }
                } else if (retArray[2]?.includes('已经入库')) {
                    judgeExist(mailNo).then(exists => {
                        if (!exists) { // 海关存在，我们没有的插入数据库
                            insert();
                        } else {
                            console.log('已存在不插入');
                            judgeExistbagno(mailNo).then(exists => {
                                if (exists) {
                                    warehousing_form.V_NOTE = retArray[retArray.length - 1] + '\n已绑定总包号，可能重复入库或二次往返入库';
                                }
                            });
                        }
                        showNotify({ type: 'warning', message: retArray[2] });
                    });
                }
            } else {
                warehousing_form.V_NOTE = '(海关接口)海关审查失败!!';
                inserterror(res.result.info, res.result.status, res.result.note);
            }
        }).catch((err: any) => {
            console.log(err);
            warehousing_form.V_NOTE = '(海关接口)海关审查失败!!';
        });
    }

    //插入数据
    function insert() {
        axios_gh('/tyrkdj', toRaw(warehousing_form)).then((res: any) => {
            if (res.success == false) {
                showDialog({ message: '入库失败' });
            } else {
                mail_num.value = parseInt(mail_num.value) + 1
            }

        }).catch((err: any) => {
            console.log(err);
            showNotify('出现错误， 原因：' + err)
        })
    }

    //更新数据库
    function update() {
        axios_gh('/gxyjxx', toRaw(warehousing_form)).then((res: any) => {
            if (res.success == false) {
                showDialog({ message: '入库失败' });
            }
        }).catch((err: any) => {
            console.log(err);
        })
    }




    return {
        search,
        searchRef,
        showHgResult,
        warehousing_form,
        onSearch,
        mail_num,
        forceMode,
        toggleForceMode,
    };
}

