<script setup lang="ts">
import "plus-pro-components/es/components/search/style/css";
import { reactive, toRaw, ref, onMounted, getCurrentInstance } from "vue";
import { useColumns } from "./index";

const {
    searchValue,
    searchRef,
    formData,
    onSearch,
    onSubmit
    
} = useColumns();

onMounted(() => {
    searchRef.value && searchRef.value.focus();
})


</script>

<template>
    <div class="TrafficScan">
        <header>
            <van-search v-model="searchValue" shape="round" background="#1989fa" placeholder="请扫描邮袋号" @search="onSearch"
                ref="searchRef" />
        </header>
        <main>
            <van-cell-group>
                <van-field v-model="formData.V_BAGNO" label="邮袋号" type="textarea" />
                <van-field v-model="formData.V_OPERNAME" name="操作人" label="操作人" disabled />
            </van-cell-group>
        </main>
        <div style="margin: 16px;">
            <van-button type="primary" block @click="onSubmit" round>交航扫描</van-button>
        </div>
    </div>
</template>

<style scoped>
main {
    padding-bottom: 50px;
}
</style>