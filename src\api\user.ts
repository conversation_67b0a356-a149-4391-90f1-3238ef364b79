import { http } from "@/utils/http";

export type UserResult = {
  success: boolean;
  data: {
    /** 头像 */
    avatar: string;
    /** 用户名 */
    username: string;
    /** 账户 */
    account: string;
    /** 昵称 */
    // nickname: string;
    /** 当前登录用户的角色 */
    roles: Array<string>;
    /** `token` */
    accessToken: string;
    /** 用于调用刷新`accessToken`的接口时所需的`token` */
    refreshToken: string;
    /** `accessToken`的过期时间（格式'xxxx/xx/xx xx:xx:xx'） */
    expires: Date;
  };
  // data: any[];
  // msg: string | null;
};

export type UserResult1 = {
  success: boolean;
  // data: {
  //   /** 头像 */
  //   avatar: string;
  //   /** 用户名 */
  //   username: string;
  //   /** 账户 */
  //   account: string;
  //   /** 昵称 */
  //   // nickname: string;
  //   /** 当前登录用户的角色 */
  //   roles: Array<string>;
  //   /** `token` */
  //   accessToken: string;
  //   /** 用于调用刷新`accessToken`的接口时所需的`token` */
  //   refreshToken: string;
  //   /** `accessToken`的过期时间（格式'xxxx/xx/xx xx:xx:xx'） */
  //   expires: Date;
  // };
  data: any[];
  msg: string | null;
};


export type RefreshTokenResult = {
  success: boolean;
  data: {
    /** `token` */
    accessToken: string;
    /** 用于调用刷新`accessToken`的接口时所需的`token` */
    refreshToken: string;
    /** `accessToken`的过期时间（格式'xxxx/xx/xx xx:xx:xx'） */
    expires: Date;
  };
};

export type UserInfo = {
  /** 头像 */
  avatar: string;
  /** 用户名 */
  username: string;
  /** 账户 */
  account: string;
  /** 昵称 */
  // nickname: string;
  /** 邮箱 */
  email: string;
  /** 联系电话 */
  phone: string;
  /** 简介 */
  description: string;
};

export type UserInfoResult = {
  success: boolean;
  data: UserInfo;
};

type ResultTable = {
  success: boolean;
  data?: {
    /** 列表数据 */
    list: Array<any>;
    /** 总条目数 */
    total?: number;
    /** 每页显示条目个数 */
    pageSize?: number;
    /** 当前页数 */
    currentPage?: number;
  };
};


let baseGhURL: string;
let baseHgURL: string;


//配置开发环境
if (process.env.NODE_ENV === 'development') {
  baseGhURL = 'http://************:8520/api/tyyj',
    baseHgURL = 'http://localhost:8848/hg'
  console.log("************");

}

//配置生产环境
if (process.env.NODE_ENV === 'production') {
  baseGhURL = 'http://************:8520/api/tyyj',
    baseHgURL = 'http://************:8899/hg'
  console.log("22222222222111111111111");
  console.log(baseHgURL);


}

//设置请求头
// const default_header1 = {
//   'Accept': 'application/json;charset=UTF-8',
//   'Content-Type': 'application/x-www-form-urlencoded',
//   'Access-Control-Allow-Origin': '*'
// }


/** 登录 */
export const getLogin = (data?: object) => {
  let gh_url = baseGhURL + "/login";
  // return http.request<UserResult>("post", "/login", { data });
  return http.request<UserResult>("post", gh_url, { data });
};

/** 刷新`token` */
export const refreshTokenApi = (data?: object) => {
  return http.request<RefreshTokenResult>("post", "/refresh-token", { data });
};

/** 账户设置-个人信息 */
export const getMine = (data?: object) => {
  return http.request<UserInfoResult>("get", "/mine", { data });
};

/** 账户设置-个人安全日志 */
export const getMineLogs = (data?: object) => {
  return http.request<ResultTable>("get", "/mine-logs", { data });
};



export const axios_gh = (url: string, data?: object | any,) => {
  return http.request<UserResult>("post", baseGhURL + url, { data });
};

export const axios_gh1 = (url: string, data?: object | any,) => {
  return http.request<UserResult1>("post", baseGhURL + url, { data });
};
export const axios_hg = (data: any) => {
  return http.request<UserResult>("post", baseHgURL, { data });
  // return axios.post(baseHgURL, data);
};

