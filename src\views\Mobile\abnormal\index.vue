<script setup lang="ts">
import "plus-pro-components/es/components/search/style/css";
import { reactive, toRaw, ref, onMounted, getCurrentInstance } from "vue";
import { useColumns } from "./index";

const {
    searchValue,
    searchRef,
    cell,
    abnormalTypeColums,
    showAbnormalTypePicker,
    showAbinfoPicker,
    showCustomAbinfoInput,
    abinfoColumns,
    afterRead,
    beforeRead,
    fileList,
    batchMode,
    batchInput,
    mailQueue,
    processing,
    processedCount,
    successCount,
    onAbnormalTypeConfirm,
    onAbinfoConfirm,
    onSearch,
    onSumbitClick,
    deleteUpload,
    processBatchInput
} = useColumns();

onMounted(() => {
    searchRef.value && searchRef.value.focus();
})

// getMine().then(res => {
//     console.log(res.data);

// });

</script>


<template>
    <div class="Abnormal">
        <header>
            <van-search v-model="searchValue" shape="round" background="#1989fa"
                :placeholder="batchMode ? '请使用下方批量输入框' : '请扫描邮件号'" @search="onSearch" ref="searchRef"
                :disabled="batchMode" />
            <div class="switch-container">
                <span class="switch-hint">批量模式</span>
                <van-switch v-model="batchMode" size="24px" />
            </div>
        </header>
        <main>
            <div v-if="batchMode" style="padding: 16px;">
                <van-field v-model="batchInput" type="textarea" rows="5" placeholder="请输入邮件号，每行一个或用逗号/分号分隔"
                    :disabled="processing" />
                <div style="margin: 16px 0;">
                    <van-button round block type="primary" @click="processBatchInput" :loading="processing"
                        :disabled="processing || !batchInput.trim()">
                        {{ processing ? `处理中 (${processedCount}/${mailQueue.length + processedCount})` : '批量提交' }}
                    </van-button>
                </div>
                <van-notice-bar v-if="processing" color="#1989fa" background="#ecf9ff">
                    正在处理: {{ successCount }}成功 {{ processedCount - successCount }}失败
                </van-notice-bar>
            </div>

            <van-form @submit="onSumbitClick">
                <van-cell-group>
                    <van-field v-if="!batchMode" v-model="cell.V_MAILNO" label="邮件号" required />
                    <van-field
                        :modelValue="cell.V_ABNORMALTYPE == '3' ? '退运已出库转退运已入库' : (cell.V_ABNORMALTYPE == '2' ? '退运已装袋转退运已入库' : '退运已入库转其他原因出库')"
                        readonly is-link name="处置类型" label="处置类型" placeholder="点击选择类型"
                        @click="showAbnormalTypePicker = true" required
                        :rules="[{ required: true, message: '请选择处置类型' }]" />
                    <van-popup v-model:show="showAbnormalTypePicker" position="bottom">
                        <van-picker :columns="abnormalTypeColums" @confirm="onAbnormalTypeConfirm"
                            @cancel="showAbnormalTypePicker = false" />
                    </van-popup>
                    <van-field v-model="cell.V_ENTNAME" label="企业名称" disabled type='textarea' />
                    <van-field v-model="cell.V_ABINFO" readonly is-link name="情况说明" label="情况说明" placeholder="点击选择情况说明"
                        @click="showAbinfoPicker = true" :rules="[{ required: true, message: '请选择情况说明' }]" />

                    <van-popup v-model:show="showAbinfoPicker" position="bottom">
                        <van-picker :columns="abinfoColumns" @confirm="onAbinfoConfirm"
                            @cancel="showAbinfoPicker = false" />
                    </van-popup>
                    <van-field v-if="showCustomAbinfoInput" v-model="cell.V_ABINFO" label="自定义说明" placeholder="请输入情况说明"
                        :rules="[{ required: true, message: '请输入情况说明' }]" />

                    <van-field v-model="cell.V_INSTRSTATE" label="指令状态" />
                    <van-field v-model="cell.V_FILENAME" label="文件名称" disabled />
                    <!-- <van-field v-model="cell.V_FILEDATA" label="文件内容" type="textarea" maxlength="125" required /> -->
                    <van-field v-model="cell.V_OPERATORNAME" label="申报人" disabled />
                    <van-divider :style="{ color: '#1989fa', borderColor: '#1989fa', padding: '0 16px' }">
                        上传文件
                    </van-divider>
                    <!-- <van-uploader :after-read="afterRead" v-model="fileList" :max-count="1" @delete="deleteUpload" /> -->

                    <van-uploader :after-read="afterRead" v-model="fileList" :max-count="1"
                        accept=".jpg,.jpeg,.png,.pdf,.doc,.docx,.xls,.xlsx,.txt" @delete="deleteUpload" />

                </van-cell-group>
                <div style="margin: 16px;" v-if="!batchMode">
                    <van-button round block type="primary" native-type="submit">
                        申报
                    </van-button>
                </div>
            </van-form>
        </main>
    </div>
</template>

<style scoped>
main {
    padding-bottom: 50px;
}

header {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px;
    background: #1989fa;
}

.van-search {
    flex: 1;
    margin-right: 8px;
}

.switch-container {
    display: flex;
    align-items: center;
    padding: 0 8px;
}

.switch-hint {
    color: white;
    font-size: 14px;
    margin-right: 8px;
}

.van-switch {
    --van-switch-on-background: white;
    --van-switch-node-background: white;
    --van-switch-size: 24px;
}

.van-notice-bar {
    margin-bottom: 16px;
}
</style>