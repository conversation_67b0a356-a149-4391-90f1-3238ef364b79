<script setup lang="ts">
import "plus-pro-components/es/components/search/style/css";
import { reactive, toRaw, ref, onMounted, getCurrentInstance } from "vue";
import { useColumns } from "./index";

const {
    searchValue,
    showTypePicker,
    showCenter,
    showCurrentWeight,
    formData,
    showCarNoPicker,
    searchRef,
    modifyValue,
    showAuditNo,
    typeColumns,
    carNoColumns,
    onTypeConfirm,
    getCarNoList,
    onCarNoConfirm,
    onDetailClick,
    onDeleteClick,
    onSearch,
    onModifyClick,
    onApplyClick,
} = useColumns();

onMounted(() => {
    searchRef.value && searchRef.value.focus();
    getCarNoList('1')
})

// getMine().then(res => {
//     console.log(res.data);

// });

</script>

<template>
    <div class="Outbound">
        <header>
            <van-search v-model="searchValue" shape="round" background="#1989fa" placeholder="请输入总包号" @search="onSearch"
                ref="searchRef" />
        </header>
        <main>
            <van-form>
                <van-cell-group>
                    <van-field v-if="modifyValue" v-model="showAuditNo" name="序列号" label="序列号" readonly disabled />
                    <van-field
                        :modelValue="formData.V_AUDIT_TYPE == '3' ? '转运' : (formData.V_AUDIT_TYPE == '2' ? '转关' : '转场')"
                        readonly is-link name="类型" label="类型" placeholder="点击选择类型" @click="showTypePicker = true"
                        required :rules="[{ required: true, message: '请选择类型' }]" />
                    <van-popup v-model:show="showTypePicker" position="bottom">
                        <van-picker :columns="typeColumns" @confirm="onTypeConfirm" @cancel="showTypePicker = false" />
                    </van-popup>
                    <van-field v-model="formData.V_CARNO" readonly is-link name="车牌号" label="车牌号" placeholder="点击选择车牌号"
                        required @click="showCarNoPicker = true" :rules="[{ required: true, message: '请选择车牌号' }]" />
                    <van-popup v-model:show="showCarNoPicker" position="bottom">
                        <van-picker :columns="carNoColumns" @confirm="onCarNoConfirm"
                            @cancel="showCarNoPicker = false" />
                    </van-popup>
                    <van-field v-model="formData.V_ADDRESS" name="目的地" label="目的地" required
                        :rules="[{ required: true, message: '请输入目的地' }]" />
                    <van-field v-model="formData.V_MAILBAG_NUM" name="数量" label="数量" is-link @click="onDetailClick"
                        readonly />
                    <van-field v-model="formData.V_MAILBAG_WEIGHT" name="总重量" label="总重量(kg)" disabled>
                        <template #right-icon>
                            <a> {{ showCurrentWeight }}</a>
                        </template>
                    </van-field>
                    <van-field v-model="formData.CUSTOMS_CODE" name="关区号" label="关区号" disabled />
                    <van-field v-model="formData.V_OPERNAME" name="操作人" label="操作人" disabled />
                </van-cell-group>
                <!--详细清单弹出框-->
                <van-popup v-model:show="showCenter" position="left" round :style="{ width: '80%', height: '100%' }">
                    <van-cell title="邮袋清单"></van-cell>
                    <van-cell v-for="(item, index) in formData.V_BAGNO" :key="index" :title="item"
                        :style="{ padding: '10px 5px' }">
                        <template #right-icon>
                            <van-icon name="close" class="close" color="#ee0a24" @click="onDeleteClick(index, item)" />
                        </template>
                    </van-cell>
                </van-popup>
                <div style="margin: 16px;">
                    <van-button v-if="modifyValue" round block type="primary" @click="onModifyClick">
                        修改
                    </van-button>
                    <van-button v-else round block type="primary" @click="onApplyClick">
                        申请
                    </van-button>
                </div>
            </van-form>
        </main>
    </div>
</template>