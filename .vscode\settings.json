{"editor.formatOnType": true, "editor.formatOnSave": true, "[vue]": {"editor.defaultFormatter": "Vue.volar"}, "editor.tabSize": 4, "editor.formatOnPaste": true, "editor.guides.bracketPairs": "active", "files.autoSave": "after<PERSON>elay", "git.confirmSync": false, "workbench.startupEditor": "newUntitledFile", "editor.suggestSelection": "first", "editor.acceptSuggestionOnCommitCharacter": false, "css.lint.propertyIgnoredDueToDisplay": "ignore", "editor.quickSuggestions": {"other": true, "comments": true, "strings": true}, "files.associations": {"editor.snippetSuggestions": "top"}, "[css]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "iconify.excludes": ["el"]}