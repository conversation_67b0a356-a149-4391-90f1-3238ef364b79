<script setup lang="ts">
import { clone, delay } from "@pureadmin/utils";
import { ref, onMounted, reactive, watchEffect, toRaw } from "vue";
import { utils, writeFile } from "xlsx";
import { message } from "@/utils/message";
import { axios_gh } from "@/api/user";
import "plus-pro-components/es/components/search/style/css";
import { type PlusColumn, PlusSearch } from "plus-pro-components";
import { useColumns } from "./index";
import MultiBagnoInput from "@/components/MultiBagnoInput/index.vue"; // 导入自定义组件

const {
    columns,
    search_columns,
    tallying_select,
    pagination,
    loadingConfig,
    dataList,
    loading,
    tableSize,
    handleChange,
    handleSearch,
    handleRest,
    onSizeChange,
    onCurrentChange,
    handleSelectionChange,
    exportExcel,
    getTallyingData,
    onPushClick,
    onStatusClick,
    onDeleteClick,
    onUpdateClick,
    onResetPushStatusClick
} = useColumns();

onMounted(() => {
    // getTallyingData(toRaw(tallying_select)).then((res: any) => {
    //     dataList.value = res;
    //     pagination.total = dataList.value.length;
    //     loading.value = false;
    // });
    handleSearch(tallying_select.value);
});



</script>

<template>
    <div>
        <PlusSearch 
            v-model="tallying_select" 
            :columns="search_columns" 
            :show-number="10" 
            label-width="100"
            :col-props="{ span: 6 }" 
            label-position="right" 
            @change="handleChange" 
            @search="handleSearch"
            search-text="查询" 
            @reset="handleRest" 
            reset-text="重置"
           
        />
        <br />
        <div class="button-group">
            <div class="left-buttons">
                <el-button @click="onStatusClick">状态</el-button>
                <el-button @click="onPushClick">推送</el-button>
            </div>
            <div class="right-buttons">
                <el-button @click="onUpdateClick">确认已绑定</el-button>
                <el-button @click="onResetPushStatusClick">重置推送状态</el-button>
                <el-button type="primary" @click="exportExcel">导出</el-button>
            </div>
        </div>
        <pure-table :data="dataList.slice(
            (pagination.currentPage - 1) * pagination.pageSize,
            pagination.currentPage * pagination.pageSize
        )" :columns="columns" alignWhole="center" border :height="tableSize === 'small' ? 352 : 460"
            :pagination="pagination" ref="waterRef" @page-size-change="onSizeChange"
            @page-current-change="onCurrentChange" @selection-change="handleSelectionChange">
            <template #operation="{ row }">
                <el-popconfirm v-if="row.V_AUDIT_NO == null" title="你确定删除?" @confirm="onDeleteClick(row)">
                    <template #reference>
                        <el-button link type="danger" size="small">
                            删除
                        </el-button>
                    </template>
                </el-popconfirm>
            </template>
        </pure-table>
    </div>
</template>

<style lang="scss" scoped>
/* 绑定失败原因的样式 */
.failure-reason {
    .truncate-text {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        cursor: pointer;
    }

    :deep(.el-tooltip__trigger) {
        display: inline-block;
        width: 100%;
    }

    :deep(.el-tooltip__content) {
        max-width: 400px;
    }
}

/* 搜索表单的样式 */
:deep(.plus-search) {
    .el-select {
        width: 100%;
        
        .el-input {
            width: 100%;
        }
    }

    .el-select-dropdown {
        .el-select-dropdown__item {
            padding: 8px 12px;
            white-space: normal !important;
            height: auto;
            line-height: 1.5;
        }
    }

    .el-form-item {
        margin-right: 10px;
        flex: 1;
        min-width: 200px;
        max-width: 300px;

        .el-form-item__label {
            white-space: nowrap;
            min-width: 90px;
        }
    }

    .el-date-editor {
        width: 100% !important;
    }
}

/* 确保下拉框内容正确显示 */
:deep(.el-select-dropdown) {
    .el-select-dropdown__wrap {
        max-height: 274px;
    }
    
    .el-select-dropdown__item {
        padding: 0 12px;
        height: auto;
        line-height: 1.5;
        white-space: normal !important;
        word-break: break-word;
        min-height: 34px;
        display: flex;
        align-items: center;
    }
}

.button-group {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .left-buttons {
        display: flex;
        gap: 10px;  /* 按钮之间的间距 */
    }

    .right-buttons {
        display: flex;
        gap: 10px;  /* 按钮之间的间距 */
    }
}
</style>
