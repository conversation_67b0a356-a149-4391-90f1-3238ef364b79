import { flag } from "@/router/utils";
export default {
  path: "/tallyingTable",
  redirect: "/tallyingTable/index",
  meta: {
    icon: "ri:shopping-bag-fill",
    title: "理货总包表",
    rank: 2
  },
  children: [
    {
      path: "/tallyingTable/index",
      name: "tallyingTable",
      component: () => import("@/views/PC/tallyingTable/index.vue"),
      meta: {
        showLink: flag ? false : true,
        title: "理货总包表",
      }
    }
  ]
} satisfies RouteConfigsTable;
