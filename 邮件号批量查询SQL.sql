-- 邮件号批量查询SQL样式
SELECT m.V_MAILNO,  
       to_char(m.D_APPTIME,'yyyy/MM/dd hh24:mi:ss') D_APPTIME,
       to_char(a.d_oper_date,'yyyy/MM/dd') D_OPER_DATE,
       to_char(a.d_out_date,'yyyy/MM/dd') D_OUT_DATE, 
       m.V_OPERNAME,
       m.V_NOTE, 
       to_char(m.D_RETURN_DATE,'yyyy-MM-dd') D_RETURN_DATE,
       m.V_BAGNO,
       V_MAILRET,
       m.V_MAILSTATUS 
FROM TB_RETURN_MAIL m,TB_RETURN_BAG b,TB_RETURN_AUDITNO a 
WHERE 1 = 1 
  AND m.V_BAGNO=b.v_bagno(+) 
  AND b.v_audit_no=a.v_audit_no(+)

<where>
    <!-- 批量查询条件：V_MAILNO 在给定列表中 -->
    <if test="V_MAILNOS != null and !V_MAILNOS.isEmpty()">
        AND m.V_MAILNO IN
        <foreach item="V_MAILNO" collection="V_MAILNOS" open="(" separator="," close=")">
            #{V_MAILNO}
        </foreach>
    </if>

    <!-- 其他可选条件 -->
    <if test="V_MAILNO != null and V_MAILNO != ''">
        AND m.V_MAILNO like '%' || #{V_MAILNO} || '%' 
    </if>

    <if test="V_OPERNAME != null and V_OPERNAME != ''">
        AND m.V_OPERNAME like '%' || #{V_OPERNAME} || '%' 
    </if>

    <if test="D_APPTIME_START != null and D_APPTIME_START != ''">
        AND to_char(m.D_APPTIME, 'yyyy-MM-dd') >= #{D_APPTIME_START}
    </if>
    <if test="D_APPTIME_END != null and D_APPTIME_END != ''">
        AND to_char(m.D_APPTIME, 'yyyy-MM-dd') <= #{D_APPTIME_END}
    </if>

    <if test="D_OPER_DATE_START != null and D_OPER_DATE_START != ''">
        AND to_char(a.d_oper_date, 'yyyy-MM-dd') >= #{D_OPER_DATE_START}
    </if>
    <if test="D_OPER_DATE_END != null and D_OPER_DATE_END != ''">
        AND to_char(a.d_oper_date, 'yyyy-MM-dd') <= #{D_OPER_DATE_END}
    </if>

    <if test="D_OUT_DATE_START != null and D_OUT_DATE_START != ''">
        AND to_char(a.d_out_date, 'yyyy-MM-dd') >= #{D_OUT_DATE_START}
    </if>
    <if test="D_OUT_DATE_END != null and D_OUT_DATE_END != ''">
        AND to_char(a.d_out_date, 'yyyy-MM-dd') <= #{D_OUT_DATE_END}
    </if>

    <if test="V_MAILRET != null and V_MAILRET != ''">
        AND m.V_MAILRET like '%' || #{V_MAILRET} || '%' 
    </if>
    <if test="V_MAILSTATUS != null and V_MAILSTATUS != ''">
        AND m.V_MAILSTATUS like '%' || #{V_MAILSTATUS} || '%' 
    </if>
    <if test="V_BAGNO != null and V_BAGNO != ''">
        AND m.V_BAGNO like '%' || #{V_BAGNO} || '%' 
    </if>
</where>

ORDER BY m.D_APPTIME desc, m.V_MAILSTATUS desc
