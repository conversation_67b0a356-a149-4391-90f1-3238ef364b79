import qs from 'qs'
import { axios_gh, axios_hg } from "@/api/user";
import { ref, reactive, toRaw } from 'vue'
import { showToast, showFailToast, showNotify, showLoadingToast, closeToast, showDialog } from 'vant'
import Cookies from 'js-cookie';

export function useColumns() {
    let searchValue = ref<string>('')
    let showCenter = ref<boolean>(false)
    let mailbagWeight = ref<Array<number>>([])
    let showCurrentWeight = ref<number>(0)

    interface form {
        V_CARNO: string,
        V_ADDRESS: string,
        V_MAILBAG_NUM: number,
        V_BAGNO: Array<string>,
        V_MAILBAG_WEIGHT: string,
        V_AUDIT_TYPE: string,
        CUSTOMS_CODE: string,
        V_OPERNAME: string
    }


    let formData: form = reactive({
        V_CARNO: '',
        V_ADDRESS: '',
        V_MAILBAG_NUM: '0',
        V_BAGNO: [],
        V_MAILBAG_WEIGHT: '0',
        V_AUDIT_TYPE: '1',
        CUSTOMS_CODE: '5147',
        V_OPERNAME: Cookies.get("USER_NAME")
    })

    let showTypePicker = ref(false)
    let showCarNoPicker = ref(false)
    let typeColumns = [
        { text: '转场', value: '1' },
        { text: '转关', value: '2' },
        { text: '转运', value: '3' },
    ]
    let carNoColumns = ref<Array<object>>([])
    let searchRef = ref<HTMLInputElement | null>(null)
    let modifyValue = ref<boolean>(false)
    let showAuditNo = ref<string>()
    let deleteBagNo = ref<Array<string>>([])

    //输入邮袋号
    function onSearch(val: string) {
        //两种总包类型
        let pattern1 = /^[0-9]{30}$/
        let pattern2 = /^[A-Z]{15}[0-9]{14}$/
        //去除头尾空格
        val = val.trim()

        if (!pattern1.test(val) && !pattern2.test(val)) {
            showFailToast('条码不符合规则')
        } else if (judgeRepeat(val)) {  //判断是否重复
            showFailToast('清单重复总包条码')
        } else {
            //获取邮袋信息
            getBagNoInfo(val)
        }
        searchValue.value = ''
        searchRef.value && searchRef.value.focus()
    }


    //判断是否重复
    function judgeRepeat(val: string) {
        let result: boolean = false
        formData.V_BAGNO.forEach((e: string) => {
            if (e === val) {
                result = true
            }
        })
        return result;
    }

    //获取邮袋信息
    function getBagNoInfo(bagno: any) {
        axios_gh('/gjydhcxxx', {
            V_BAGNO: bagno
        }).then((res: any) => {
            let data = res.data[0]
            if (res.data.length == 0) {
                showFailToast('该总包未理货采集')
            } else if (data.V_LHTJFLAG == '0') {
                showFailToast('该总包未推给海关')
            } else if (data.V_AUDIT_NO != null && data.V_CHECK == '1') {
                showDialog({
                    title: '提示',
                    message: '该总包已经出库申请且审核通过，不允许修改！！',
                })
            } else if (data.V_AUDIT_NO != null) { //申请过
                showDialog({
                    title: '提示',
                    message: '该总包已经出库申请过，是否进入修改',
                    showCancelButton: true
                }).then((res: any) => { //进入修改
                    modifyValue.value = true
                    //获取修改内容
                    getModifyData(data.V_AUDIT_NO)
                }).catch((err: any) => {
                    // showToast('扫描成功')
                    // formData.V_BAGNO.unshift(bagno)
                    // mailbagWeight.value.unshift(data.N_BAGWEIGHT)

                    // //计算总重量、数量
                    // formData.V_MAILBAG_WEIGHT = (parseFloat(data.N_BAGWEIGHT) + parseFloat(formData.V_MAILBAG_WEIGHT)).toFixed(2)
                    // formData.V_MAILBAG_NUM = formData.V_BAGNO.length
                    // showCurrentWeight.value = data.N_BAGWEIGHT
                    // 添加邮袋信息
                    // addBagInfo(bagno, data.N_BAGWEIGHT);
                    // 用户点击取消或关闭弹窗，什么都不做
                    // 不添加邮袋信息
                })
            } else {
                // showToast('扫描成功')
                // formData.V_BAGNO.unshift(bagno)
                // mailbagWeight.value.unshift(data.N_BAGWEIGHT)

                // //计算总重量、数量
                // formData.V_MAILBAG_WEIGHT = (parseFloat(data.N_BAGWEIGHT) + parseFloat(formData.V_MAILBAG_WEIGHT)).toFixed(2)
                // formData.V_MAILBAG_NUM = formData.V_BAGNO.length
                // showCurrentWeight.value = data.N_BAGWEIGHT
                // 添加邮袋信息
                addBagInfo(bagno, data.N_BAGWEIGHT);
            }
        }).catch((err: any) => {
            console.log(err)
            // instance.appContext.config.globalProperties.$judgeError(err.message)
            showNotify({ message: '出现错误， 原因：' + err })
        })
    }

    // 添加邮袋信息到 formData
    function addBagInfo(bagno: string, bagWeight: number) {
        showToast('扫描成功');
        // 如果邮袋号不存在，则添加
        if (!formData.V_BAGNO.includes(bagno)) {
            formData.V_BAGNO.unshift(bagno); // 添加邮袋号到 V_BAGNO 列表
            mailbagWeight.value.unshift(bagWeight); // 添加邮袋重量

            // 更新总重量和数量
            formData.V_MAILBAG_WEIGHT = (parseFloat(bagWeight) + parseFloat(formData.V_MAILBAG_WEIGHT)).toFixed(2);
            formData.V_MAILBAG_NUM = formData.V_BAGNO.length;
            showCurrentWeight.value = bagWeight;
        } else {
            showNotify('该邮袋号已存在');
        }
    }
    //申请
    // function onApplyClick() {
    //     //构建海关接口所需参数
    //     let params = []
    //     for (let key in formData) {
    //         if (key == 'V_BAGNO') {
    //             params.push(formData.V_BAGNO.join(','))
    //         } else {
    //             params.push(formData[key])
    //         }
    //     }
    //     let new_params = (JSON.stringify(params)).replace(/\"/g, "'")
    //     outbandAuditAddReport(new_params)
    // }
    function onApplyClick() {
        // 构建海关接口所需参数
        let params = [];
        let hasEmptyValue = false; // 标识是否存在空值

        for (let key in formData) {
            // 判断当前 key 的值是否为空
            if (!formData[key] || (Array.isArray(formData[key]) && formData[key].length === 0)) {
                hasEmptyValue = true;
                break;
            }

            if (key === 'V_BAGNO') {
                params.push(formData.V_BAGNO.join(','));
            } else {
                params.push(formData[key]);
            }
        }

        // 如果存在空值，显示提示信息并退出
        if (hasEmptyValue) {
            showNotify('请填写所有必填参数');
            return;
        }

        // 构建参数并调用接口
        let new_params = JSON.stringify(params).replace(/\"/g, "'");
        outbandAuditAddReport(new_params);
    }

    //获取修改内容
    // function getModifyData(auditno: string) {
    //     axios_gh('/cxxlhxx', {
    //         V_AUDIT_NO: auditno
    //     }).then((res: any) => {
    //         let data = res.data[0]
    //         showAuditNo.value = data.V_AUDIT_NO
    //         formData.V_AUDIT_TYPE = data.V_AUDIT_TYPE
    //         formData.V_CARNO = data.V_CARNO
    //         formData.V_ADDRESS = data.V_ADDRESS
    //         formData.V_MAILBAG_NUM = data.V_MAILBAG_NUM
    //         formData.V_MAILBAG_WEIGHT = data.V_MAILBAG_WEIGHT
    //         data.V_BAGNO.split(',').forEach((e: string) => {
    //             formData.V_BAGNO.push(e)
    //         })
    //         data.N_BAGWEIGHT.split(',').forEach((e: number) => {
    //             mailbagWeight.value.push(e)
    //         })

    //     }).catch((err: any) => {
    //         console.log(err)
    //         showNotify('出现错误' + err)
    //     })
    // }
    function getModifyData(auditno: string) {
        axios_gh('/cxxlhxx', {
            V_AUDIT_NO: auditno
        }).then(async (res: any) => {
            let data = res.data[0];

            // 更新主数据
            showAuditNo.value = data.V_AUDIT_NO;
            formData.V_AUDIT_TYPE = data.V_AUDIT_TYPE;
            formData.V_CARNO = data.V_CARNO;
            formData.V_ADDRESS = data.V_ADDRESS;
            formData.V_MAILBAG_NUM = data.V_MAILBAG_NUM;
            formData.V_MAILBAG_WEIGHT = data.V_MAILBAG_WEIGHT;

            // 清空原有的邮袋号和重量数据
            formData.V_BAGNO = [];
            mailbagWeight.value = [];

            try {
                // 使用 getBagNoInfodetail 获取详细的邮袋号和重量数据
                const bagData = await getBagNoInfodetail({ V_AUDIT_NO: auditno });
                console.log("Bag Data:", bagData); // 调试输出获取的数据

                // 将获取的 bagData 填充到 formData.V_BAGNO 和 mailbagWeight.value 中
                bagData.forEach((item: any) => {
                    formData.V_BAGNO.push(item.V_BAGNO);
                    mailbagWeight.value.push(item.N_BAGWEIGHT);
                });
            } catch (error) {
                console.error("获取邮袋详情失败:", error);
                showNotify('获取邮袋详情失败：' + error);
            }
        }).catch((err: any) => {
            console.log(err);
            showNotify('出现错误：' + err);
        });
    }


    const getBagNoInfodetail = (object?: any) => {
        return axios_gh('/gjckxlhcxydh', object).then((res: any) => {
            let data = res.data
            for (let i = 0; i < data.length; i++) {
                data[i]["key"] = i
            }
            // tableData = []
            // tableData = data
            return data
            // message('查询成功', { type: 'success' })
        })
    }

    //修改
    function onModifyClick() {
        let params = []
        for (let key in formData) {
            if (key == 'V_BAGNO') {
                params.push(formData.V_BAGNO.join(','))
            } else {
                params.push(formData[key])
            }
        }
        params.unshift(showAuditNo.value)
        let new_params = (JSON.stringify(params)).replace(/\"/g, "'")
        console.log(new_params)
        outbandAuditUpdateReport(new_params)
    }


    //获取车牌号列表
    function getCarNoList(type: string) {
        axios_gh('/clxxcx', {
            V_CARNO: '',
            V_ENT_TYPE: type
        }).then((res: any) => {
            carNoColumns.value = []
            res.data.forEach((element: any) => {
                if (element.V_CHECK == '1') {
                    let object = { text: element.V_CARNO, value: element.V_CARNO }
                    carNoColumns.value.push(object)
                }
            });

        }).catch((err: any) => {
            console.log(err);
            showFailToast('获取车牌号列表出现错误')
        })


    }


    //海关-装车出库申请录入接口,获取出库申请序列号
    async function outbandAuditAddReport(new_params: string) {

        showLoadingToast({
            message: '申请中...',
            forbidClick: true,
            duration: 0,
        })

        await axios_hg(
            {
                service: 'RetpostMailService',
                method: 'outbandAuditAddReport',
                params: new_params
            }
        ).then((res: any) => {
            let data = res.result.info[0]
            if (data.desc == '录入成功') {
                showToast('海关申请录入成功')
                insertAuditNo(data.auditNo)
            } else {
                showNotify({ message: '(海关接口)申请失败， 原因：' + data.desc })
                inserterror(data.desc, res.result.status, res.result.note)
            }
        }).catch((err: any) => {
            console.log(err);
            showNotify({ message: '(海关接口)出现错误， 原因：' + err })

        })
        closeToast()
    }

    //插入数据库
    function insertAuditNo(auditNo: string) {
        Object.assign(toRaw(formData), { V_AUDIT_NO: auditNo })
        axios_gh('/insertauditno', toRaw(formData)
        ).then((res: any) => {
            console.log(res)
            let bagNo = formData.V_BAGNO
            if (bagNo.length > 0) {
                updateBag(auditNo, bagNo)
            }
            //申请成功后清空数据
            formData = reactive({
                V_CARNO: '',
                V_ADDRESS: '',
                V_MAILBAG_NUM: '0',
                V_BAGNO: [],
                V_MAILBAG_WEIGHT: '0',
                V_AUDIT_TYPE: '1',
                CUSTOMS_CODE: '5147',
                V_OPERNAME: Cookies.get("USER_NAME"),
            })
        }).catch((err: any) => {
            console.log(err);
            // instance.appContext.config.globalProperties.$judgeError(err.message)
            showNotify({ message: '出现错误， 原因：' + err })
        })
    }

    //更新BAG表
    function updateBag(auditNo: string, bagNo: Array<string>) {
        axios_gh('/updatebagauditno',
            qs.stringify(
                {
                    V_BAGNO: bagNo,
                    V_AUDIT_NO: auditNo
                },
                { indices: false })
        ).then((res: any) => {
            console.log(res)
        }).catch((err: any) => {
            console.log(err)
            // instance.appContext.config.globalProperties.$judgeError(err.message)
            showNotify({ message: '出现错误， 原因：' + err })
        })
    }

    //插入error表
    function inserterror(V_INFO: string | any[], V_STATUS: string, V_NOTE: string) {
        // 处理 V_INFO 是数组的情况
        let processedInfo = V_INFO;
        if (Array.isArray(V_INFO)) {
            processedInfo = V_INFO.map(item => {
                return `邮件号: ${item.mailNo}, 原因: ${item.mailRet}`;
            }).join('; ');
        }

        axios_gh('/errorresult',
            qs.stringify(
                {
                    V_INFO: processedInfo,
                    V_STATUS: V_STATUS,
                    V_NOTE: V_NOTE
                },
                { indices: false })
        ).then((res: any) => {
            console.log(res)
        }).catch((err: any) => {
            console.log(err)
            showNotify({ message: '出现错误， 原因：' + err })
        })
    }
    //更新Auditno表
    function updateAuditNo() {
        axios_gh('/updateauditno2',
            Object.assign(toRaw(formData), { V_AUDIT_NO: showAuditNo.value })
        ).then((res: any) => {
            console.log(res)

        }).catch((err: any) => {
            console.log(err)
            showNotify('出现错误，原因：' + err)
        })
    }

    //海关-装车出库申请修改接口
    function outbandAuditUpdateReport(new_params: string) {
        axios_hg({
            service: 'RetpostMailService',
            method: 'outbandAuditUpdateReport',
            params: new_params
        }).then((res: any) => {
            let data = res.result.info[0]
            if (data.flag == 1) { //修改成功
                // 检查是否有要删除的邮袋号
                if (deleteBagNo.value.length > 0) {
                    // 将要删除的邮袋号传入 updateBag
                    updateBag('', [...new Set(deleteBagNo.value)]);

                    // 清空删除列表
                    deleteBagNo.value = [];
                }
                let bagNo = formData.V_BAGNO
                if (bagNo.length > 0) {
                    updateBag(showAuditNo.value, bagNo)
                }
                //更新序列号，车辆等其他信息
                updateAuditNo()

            } else {
                showNotify('(海关接口)申请修改失败， 原因：' + data.desc)
                inserterror(data.desc, res.result.status, res.result.note)
            }

        }).catch((err: any) => {
            console.log(err)
            showNotify('(海关接口)出现错误，原因：' + err)
        })
    }

    //类型选择确定
    function onTypeConfirm({ selectedOptions }: any) {
        formData.V_AUDIT_TYPE = selectedOptions[0].value
        showTypePicker.value = false
        getCarNoList(selectedOptions[0].value)
        formData.V_CARNO = ''
    }

    //车牌号选择确定
    function onCarNoConfirm({ selectedOptions }: any) {
        formData.V_CARNO = selectedOptions[0].value
        showCarNoPicker.value = false
    }

    //清单详细
    function onDetailClick() {
        showCenter.value = true
    }

    //清单删除
    function onDeleteClick(index: number, item?: any) {
        //删除邮袋号
        deleteBagNo.value.push(formData.V_BAGNO[index])

        //获取删除邮袋的重量
        let currentWeight = mailbagWeight.value[index]

        //删除邮袋号和邮袋重量
        formData.V_BAGNO.splice(index, 1)
        mailbagWeight.value.splice(index, 1)

        //更新总重量和数量
        formData.V_MAILBAG_WEIGHT = (parseFloat(formData.V_MAILBAG_WEIGHT) - parseFloat(currentWeight)).toFixed(2)
        formData.V_MAILBAG_NUM = formData.V_BAGNO.length
        showCurrentWeight.value = 0 - currentWeight

        showToast('删除成功')
    }




    return {
        searchValue,
        showTypePicker,
        mailbagWeight,
        showCenter,
        showCurrentWeight,
        formData,
        showCarNoPicker,
        deleteBagNo,
        searchRef,
        modifyValue,
        showAuditNo,
        typeColumns,
        carNoColumns,
        onTypeConfirm,
        getCarNoList,
        onCarNoConfirm,
        onDetailClick,
        onDeleteClick,
        outbandAuditUpdateReport,
        onSearch,
        onModifyClick,
        onApplyClick,
    };
}
