{"name": "pure-admin-thin", "version": "5.6.0", "private": true, "type": "module", "scripts": {"dev": "NODE_OPTIONS=--max-old-space-size=4096 vite", "serve": "pnpm dev", "build": "rimraf dist && NODE_OPTIONS=--max-old-space-size=8192 vite build", "build:staging": "rimraf dist && vite build --mode staging", "report": "rimraf dist && vite build", "preview": "vite preview", "preview:build": "pnpm build && vite preview", "typecheck": "tsc --noEmit && vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "svgo": "svgo -f . -r", "clean:cache": "rimraf .eslintcache && rimraf pnpm-lock.yaml && rimraf node_modules && pnpm store prune && pnpm install", "lint:eslint": "eslint --cache --max-warnings 0  \"{src,mock,build}/**/*.{vue,js,ts,tsx}\" --fix", "lint:prettier": "prettier --write  \"src/**/*.{js,ts,json,tsx,css,scss,vue,html,md}\"", "lint:stylelint": "stylelint --cache --fix \"**/*.{html,vue,css,scss}\" --cache-location node_modules/.cache/stylelint/", "lint": "pnpm lint:eslint && pnpm lint:prettier && pnpm lint:stylelint", "prepare": "husky", "preinstall": "npx only-allow pnpm"}, "keywords": ["pure-admin-thin", "vue-pure-admin", "element-plus", "tailwindcss", "pure-admin", "typescript", "pinia", "vue3", "vite", "esm"], "homepage": "https://github.com/pure-admin/pure-admin-thin", "repository": {"type": "git", "url": "git+https://github.com/pure-admin/pure-admin-thin.git"}, "bugs": {"url": "https://github.com/pure-admin/vue-pure-admin/issues"}, "license": "MIT", "author": {"name": "xiaoxian521", "email": "<EMAIL>", "url": "https://github.com/xiaoxian521"}, "dependencies": {"@nutui/nutui": "^3.2.7", "@pureadmin/descriptions": "^1.2.1", "@pureadmin/table": "^3.1.2", "@pureadmin/utils": "^2.4.7", "@vueuse/core": "^10.10.0", "@vueuse/motion": "^2.1.0", "animate.css": "^4.1.1", "axios": "^1.7.2", "cropperjs": "1.6.2", "dayjs": "^1.11.11", "echarts": "^5.5.0", "element-plus": "^2.7.3", "i": "^0.3.7", "js-cookie": "^3.0.5", "js-md5": "0.7.3", "localforage": "^1.10.0", "mitt": "^3.0.1", "nprogress": "^0.2.0", "path": "^0.12.7", "pinia": "^2.1.7", "pinyin-pro": "^3.21.0", "plus-pro-components": "^0.1.11", "pnpm": "^9.7.0", "qs": "^6.12.1", "responsive-storage": "^2.2.0", "sortablejs": "^1.15.2", "vant": "^4.9.2", "vue": "^3.4.27", "vue-router": "^4.3.2", "vue-tippy": "^6.4.1", "vue-types": "^5.1.2", "xlsx": "^0.18.5"}, "devDependencies": {"@commitlint/cli": "^19.3.0", "@commitlint/config-conventional": "^19.2.2", "@commitlint/types": "^19.0.3", "@eslint/js": "^9.3.0", "@faker-js/faker": "^8.4.1", "@iconify-icons/ep": "^1.2.12", "@iconify-icons/ri": "^1.2.10", "@iconify/vue": "^4.1.2", "@pureadmin/theme": "^3.2.0", "@types/gradient-string": "^1.1.6", "@types/js-cookie": "^3.0.6", "@types/node": "^20.12.12", "@types/nprogress": "^0.2.3", "@types/qs": "^6.9.15", "@types/sortablejs": "^1.15.8", "@typescript-eslint/eslint-plugin": "^7.11.0", "@typescript-eslint/parser": "^7.11.0", "@vant/auto-import-resolver": "^1.2.1", "@vitejs/plugin-vue": "^5.0.4", "@vitejs/plugin-vue-jsx": "^3.1.0", "autoprefixer": "^10.4.19", "boxen": "^7.1.1", "cssnano": "^7.0.1", "eslint": "^9.3.0", "eslint-config-prettier": "^9.1.0", "eslint-define-config": "^2.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-vue": "^9.26.0", "gradient-string": "^2.0.2", "husky": "^9.0.11", "lint-staged": "^15.2.5", "postcss": "^8.4.38", "postcss-html": "^1.7.0", "postcss-import": "^16.1.0", "postcss-scss": "^4.0.9", "prettier": "^3.2.5", "rimraf": "^5.0.7", "rollup-plugin-visualizer": "^5.12.0", "sass": "^1.77.2", "stylelint": "^16.6.0", "stylelint-config-recess-order": "^5.0.1", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard-scss": "^13.1.0", "stylelint-prettier": "^5.0.0", "svgo": "^3.3.2", "tailwindcss": "^3.4.3", "typescript": "^5.4.5", "unplugin-auto-import": "^0.18.0", "unplugin-vue-components": "^0.25.0", "vite": "^5.2.11", "vite-plugin-cdn-import": "^1.0.1", "vite-plugin-compression": "^0.5.1", "vite-plugin-fake-server": "^2.1.1", "vite-plugin-remove-console": "^2.2.0", "vite-plugin-router-warn": "^1.0.0", "vite-svg-loader": "^5.1.0", "vue-eslint-parser": "^9.4.2", "vue-tsc": "^1.8.27"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0", "pnpm": ">=9"}, "pnpm": {"allowedDeprecatedVersions": {"sourcemap-codec": "*", "domexception": "*", "w3c-hr-time": "*", "stable": "*", "abab": "*"}, "peerDependencyRules": {"allowedVersions": {"eslint": "9"}}}}