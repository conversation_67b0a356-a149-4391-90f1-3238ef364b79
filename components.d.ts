/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
import '@vue/runtime-core'

export {}

declare module '@vue/runtime-core' {
  export interface GlobalComponents {
    Amap: typeof import('./src/components/ReMap/src/Amap.vue')['default']
    Control: typeof import('./src/components/ReFlowChart/src/Control.vue')['default']
    DataDialog: typeof import('./src/components/ReFlowChart/src/DataDialog.vue')['default']
    NodePanel: typeof import('./src/components/ReFlowChart/src/NodePanel.vue')['default']
    ReDialog: typeof import('./src/components/ReDialog/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    Select: typeof import('./src/components/ReIcon/src/Select.vue')['default']
    Src: typeof import('./src/components/ReAnimateSelector/src/index.vue')['default']
  }
}
