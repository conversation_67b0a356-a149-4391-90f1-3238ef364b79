import { clone, delay } from "@pureadmin/utils";
import { ref, onMounted, reactive, watchEffect, toRaw } from "vue";
import type { PaginationProps, AdaptiveConfig, LoadingConfig, Align } from "@pureadmin/table";
import { utils, writeFile } from "xlsx";
import { message } from "@/utils/message";
import { axios_gh, axios_hg } from "@/api/user";
import "plus-pro-components/es/components/search/style/css";
import { type PlusColumn, PlusSearch, FieldValues } from "plus-pro-components";
import axios from "axios";
import { ElDatePicker } from "element-plus";
import dayjs from 'dayjs';
import MultiMailnoInput from "@/components/MultiMailnoInput/index.vue"; // 导入自定义组件

export function useColumns() {

    const dataList = ref([]);
    const loading = ref(true);
    const tableSize = ref("default");

    let columns: TableColumnList = [
        {
            type: "selection",
            align: "left"
        },
        {
            label: "邮件号",
            prop: "V_MAILNO",
            width: 140,
            fixed: "left"
        },
        {
            label: "退运指令时间",
            prop: "D_RETURN_DATE",
            width: 140
        },
        {
            label: '入库时间',
            prop: 'D_APPTIME',
            width: 140
        },
        {
            label: '出库申请时间',
            prop: 'D_OPER_DATE',
            width: 140
        },
        {
            label: '出库时间',
            prop: 'D_OUT_DATE',
            width: 140
        },
        {
            label: '操作人',
            prop: 'V_OPERNAME',
        },
        {
            label: '邮袋号',
            prop: 'V_BAGNO',
            width: 300,
        },
        {
            label: '查验结果',
            prop: 'V_MAILRET',
        },
        {
            label: '邮件当前状态',
            prop: 'V_MAILSTATUS',
            cellRenderer: ({ row }) => {
                switch (row.V_MAILSTATUS) {
                    case '邮件不存在':
                        return <el-tag type="danger" effect="dark">
                            {row.V_MAILSTATUS}
                        </el-tag>;
                    case '退运已出库':
                        return <el-tag type="primary" effect="dark">
                            {row.V_MAILSTATUS}
                        </el-tag>;
                    case '退运已入库':
                        return <el-tag type="warning" effect="dark">
                            {row.V_MAILSTATUS}
                        </el-tag>;
                    case '退运已装袋':
                        return <el-tag type="success" effect="dark">
                            {row.V_MAILSTATUS}
                        </el-tag>;
                    default:
                        return <el-tag type="info">
                            {row.V_MAILSTATUS}
                        </el-tag>;
                }
            }
        },
        {
            label: "操作",
            width: "120",
            fixed: "right",
            slot: "operation"
        }
    ];

    let search_columns: PlusColumn[] = [
        {
            label: "邮件号",
            prop: "V_MAILNO",
            // 使用自定义组件
            component: MultiMailnoInput,
            componentProps: {
                placeholder: "请输入邮件号，支持批量",
                rows: 3
            },
            fieldProps: {
                placeholder: "支持批量"
            }
        },
        {
            label: "入库时间",
            prop: "D_APPTIME",
            valueType: "date-picker",
            fieldProps: {
                type: "daterange",
                startPlaceholder: "开始时间",
                endPlaceholder: "结束时间",
                valueFormat: "YYYY-MM-DD"
            }
        },
        {
            label: "出库申请时间",
            prop: "D_OPER_DATE",
            valueType: "date-picker",
            fieldProps: {
                type: "daterange",
                startPlaceholder: "开始时间",
                endPlaceholder: "结束时间",
                valueFormat: "YYYY-MM-DD"
            }
        },
        {
            label: "出库时间",
            prop: "D_OUT_DATE",
            valueType: "date-picker",
            fieldProps: {
                type: "daterange",
                startPlaceholder: "开始时间",
                endPlaceholder: "结束时间",
                valueFormat: "YYYY-MM-DD"
            }
        },
        {
            label: "操作人",
            prop: "V_OPERNAME",
            fieldProps: {
                placeholder: "请输入操作人"
            }
        },
        {
            label: "查验结果",
            prop: "V_MAILRET",
            valueType: "select",
            fieldProps: {
                placeholder: "全部"
            },
            options: [
                {
                    label: "全部",
                    value: "",
                },
                {
                    label: "邮件未见异常",
                    value: "邮件未见异常",
                },
                {
                    label: "邮件发现异常",
                    value: "邮件发现异常",
                },
                {
                    label: "邮件号或者序列号异常",
                    value: "邮件号或者序列号异常",
                }
            ]
        },
        {
            label: "邮件状态",
            prop: "V_MAILSTATUS",
            valueType: "select",
            fieldProps: {
                placeholder: "全部"
            },
            options: [
                {
                    label: "全部",
                    value: "",
                },
                {
                    label: "退运已入库",
                    value: "退运已入库",
                },
                {
                    label: "退运已装袋",
                    value: "退运已装袋",
                },
                {
                    label: "退运已出库",
                    value: "退运已出库",
                }
            ]
        },
        {
            label: "邮袋号",
            prop: "V_BAGNO",
            fieldProps: {
                placeholder: "请输入邮袋号"
            }
        },
    ];


    // let tableData = [];
    let multipleSelection = ref([]);

    /** 分页配置 */
    const pagination = reactive<PaginationProps>({
        pageSize: 10,
        currentPage: 1,
        pageSizes: [10, 50, 200, 500, 1000],
        total: 0,
        align: "center",
        background: true,
        small: false
    });

    /** 加载动画配置 */
    const loadingConfig = reactive<LoadingConfig>({
        text: "正在加载第一页...",
        viewBox: "-10, -10, 50, 50",
        spinner: `
        <path class="path" d="
            M 30 15
            L 28 17
            M 25.61 25.61
            A 15 15, 0, 0, 1, 15 30
            A 15 15, 0, 1, 1, 27.99 7.5
            L 15 15
        " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>
        `
        // svg: "",
        // background: rgba()
    });

    /** 撑满内容区自适应高度相关配置 */
    const adaptiveConfig: AdaptiveConfig = {
        /** 表格距离页面底部的偏移量，默认值为 `96` */
        offsetBottom: 110
        /** 是否固定表头，默认值为 `true`（如果不想固定表头，fixHeader设置为false并且表格要设置table-layout="auto"） */
        // fixHeader: true
        /** 页面 `resize` 时的防抖时间，默认值为 `60` ms */
        // timeout: 60
        /** 表头的 `z-index`，默认值为 `100` */
        // zIndex: 100
    };

    function onChange(val) {
        pagination.small = val;
    }

    function onSizeChange(val) {
        console.log("onSizeChange", val);
    }

    function onCurrentChange(val) {
        loadingConfig.text = `正在加载第${val}页...`;
        loading.value = true;
        delay(600).then(() => {
            loading.value = false;
        });
    }

    const exportExcel = () => {
        const res = dataList.value.map(item => {
            const arr = [];
            columns.forEach((column, index) => {
                if (index > 0) {
                    arr.push(item[column.prop as string]);
                }
            });
            return arr;
        });
        const titleList = [];
        columns.forEach((column, index) => {
            if (index > 0) {
                titleList.push(column.label);
            }
        });
        res.unshift(titleList);
        const workSheet = utils.aoa_to_sheet(res);
        //设置列宽
        workSheet["!cols"] = [
            { wpx: 100 },
            { wpx: 80 },
            { wpx: 100 },
            { wpx: 100 },
            { wpx: 200 },
            { wpx: 150 },
            { wpx: 100 },
        ]

        const workBook = utils.book_new();
        utils.book_append_sheet(workBook, workSheet, "入库邮件");
        writeFile(workBook, "入库邮件表" + new Date().getTime() + ".xlsx");
        message("导出成功", {
            type: "success"
        });
    };

    const handleSelectionChange = val => {
        multipleSelection.value = val;
    };

    // watchEffect(() => {
    //     pagination.align = paginationAlign.value as Align;
    // });

    //默认查询数据
    let mailno_select = ref<FieldValues>({
        V_MAILNO: '',
        D_APPTIME: [
            dayjs().format('YYYY-MM-DD'),  // 今天开始
            dayjs().format('YYYY-MM-DD')   // 今天结束
        ],
        D_OPER_DATE: [],
        D_OUT_DATE: [],
        V_OPERNAME: '',
        V_MAILRET: '',
        V_MAILSTATUS: '',
        V_BAGNO: ''
    });


    const handleChange = (values: any) => {
        console.log(values, "change");
    };
    const handleSearch = (values: any) => {
        // 阻止默认事件，防止页面跳转
        if (event) {
            event.preventDefault?.();
        }

        // 处理邮件号，将文本框中的多行文本转换为数组
        const mailnoValue = values.V_MAILNO || '';
        const mailnos = mailnoValue.split(/[\n,，\s]/).map(item => item.trim()).filter(Boolean);

        // 如果只有一个邮件号或没有邮件号，使用单个查询接口
        if (mailnos.length <= 1) {
            const searchParams = {
                ...values,
                V_MAILNO: mailnos[0] || '',
                D_APPTIME_START: values.D_APPTIME?.[0] || '',
                D_APPTIME_END: values.D_APPTIME?.[1] || '',
                D_OPER_DATE_START: values.D_OPER_DATE?.[0] || '',
                D_OPER_DATE_END: values.D_OPER_DATE?.[1] || '',
                D_OUT_DATE_START: values.D_OUT_DATE?.[0] || '',
                D_OUT_DATE_END: values.D_OUT_DATE?.[1] || '',
            };

            // 删除原始日期范围字段
            delete searchParams.D_APPTIME;
            delete searchParams.D_OPER_DATE;
            delete searchParams.D_OUT_DATE;

            getMailNoData(toRaw(searchParams)).then((res) => {
                dataList.value = res;
                pagination.total = dataList.value.length;
                loading.value = false;
                message('查询成功', { type: 'success' });
            });
        }
        // 如果有多个邮件号，使用多邮件号查询接口
        else {
            const searchParams = {
                ...values,
                V_MAILNOS: mailnos,
                D_APPTIME_START: values.D_APPTIME?.[0] || '',
                D_APPTIME_END: values.D_APPTIME?.[1] || '',
                D_OPER_DATE_START: values.D_OPER_DATE?.[0] || '',
                D_OPER_DATE_END: values.D_OPER_DATE?.[1] || '',
                D_OUT_DATE_START: values.D_OUT_DATE?.[0] || '',
                D_OUT_DATE_END: values.D_OUT_DATE?.[1] || '',
            };

            // 删除原始字段
            delete searchParams.V_MAILNO;
            delete searchParams.D_APPTIME;
            delete searchParams.D_OPER_DATE;
            delete searchParams.D_OUT_DATE;

            getMailNoDataMulti(toRaw(searchParams)).then((res) => {
                dataList.value = res;
                pagination.total = dataList.value.length;
                loading.value = false;
                message('查询成功', { type: 'success' });
            }).catch(err => {
                console.error('查询失败:', err);
                message('查询失败', { type: 'error' });
                loading.value = false;
            });
        }
    };
    const handleRest = () => {
        mailno_select.value = {
            V_MAILNO: '',
            D_APPTIME: [],
            D_OPER_DATE: [],
            D_OUT_DATE: [],
            V_OPERNAME: '',
            V_MAILRET: '',
            V_MAILSTATUS: '',
            V_BAGNO: ''
        };
        handleSearch(mailno_select.value);
    };
    //获取数据
    function getMailNoData(object?: any) {
        return axios_gh('/cxyjxx', object).then((res: any) => {
            let data = res.data
            for (let i = 0; i < data.length; i++) {
                data[i]["key"] = i
            }
            return data
        })
    }

    //获取邮件数据（多邮件号查询）
    async function getMailNoDataMulti(object?: any) {
        return axios_gh('/cxyjxxMulti', object).then((res: any) => {
            let data = res.data
            for (let i = 0; i < data.length; i++) {
                data[i]["key"] = i
            }
            return data
        })
    }

    /**
     *  查验
     */

    //查询邮件查验结果
    function onCheckClick() {
        let data_array = toRaw(multipleSelection.value);
        let len = data_array.length;
        if (len > 0) {
            for (let i = 0; i < len; i++) {
                //获取序列号,邮件号
                let auditno = data_array[i].V_AUDIT_NO
                let mailno = data_array[i].V_MAILNO
                if (auditno != null) {
                    mailbagCheckReport(auditno, mailno);
                } else {
                    message('未进行出库申请，无法查验', { type: 'error' })
                }
            }
        } else {
            message('请选择数据', { type: 'error' })
        }
    }


    //邮件查验结果查询
    function mailbagCheckReport(auditno: string, mailno: string) {
        let params = [auditno, mailno]
        let new_params = JSON.stringify(params).replace(/\"/g, "'")
        axios_hg({
            service: 'RetpostMailService',
            method: 'mailbagCheckReport',
            params: new_params
        }).then((res: any) => {
            if (res.succeeded == true) {
                let data = res.result.info[0]
                let mailNo = data.mailNo
                let mailRet = data.mailRet
                //更新查验结果
                updateMailRet(mailNo, mailRet)
                message('(海关接口)查验成功', { type: 'success' })
            } else {
                message('(海关接口)查验失败', { type: 'error' })
            }
        })
    }



    //更新邮件查验结果
    function updateMailRet(mailNo: string, mailRet: string) {
        axios_gh('/updatemailret', {
            V_MAILNO: mailNo,
            V_MAILRET: mailRet
        })
    }


    /**
     *  状态
     */

    //查询邮件状态
    function onStatusClick() {
        let data_array = toRaw(multipleSelection.value);
        let len = data_array.length;
        if (len > 0) {
            for (let i = 0; i < len; i++) {
                console.log(data_array[i].V_MAILNO);
                //获取邮件号
                let mailno = data_array[i].V_MAILNO
                mailQueryReport(mailno);
            }
        } else {
            message('请选择数据', { type: 'error' })
        }
    }

    //海关-邮件信息查询状态
    function mailQueryReport(mailno: string) {
        let params = [mailno]
        let new_params = JSON.stringify(params).replace(/\"/g, "'")
        console.log(new_params);

        axios_hg({
            service: 'RetpostMailService',
            method: 'mailQueryReport',
            params: new_params
        }).then((res: any) => {
            let data = res.result.info[0]
            let status = data.mailStatus
            message(status, { type: 'success' })
            updateMailStatus(mailno, status)
        })
    }

    //更新邮件状态信息
    function updateMailStatus(mailno: string, status: string) {
        axios_gh('/updatemailstatus', {
            V_MAILNO: mailno,
            V_MAILSTATUS: status
        }).then(() => {
            handleSearch(mailno_select.value)
            // getMailNoData(mailno_select).then((res: any) => {
            //     dataList.value = res;
            //     pagination.total = dataList.value.length;
            //     loading.value = false;
            // })
        }).catch((err: any) => {
            console.log(err)
            message('更新邮件状态失败', { type: 'error' })
        })
    }

    // 添加删除函数
    function onDeleteClick(row) {
        // 检查邮袋号是否为空
        if (row.V_BAGNO) {
            message('该邮件已装袋，不能删除', { type: 'warning' });
            return;
        }

        return axios_gh('/deletemailno', {
            V_MAILNO: row.V_MAILNO
        }).then((res: any) => {
            if (res.success) {
                message('删除成功', { type: 'success' });
                // 刷新数据
                handleSearch(mailno_select.value);
            } else {
                message('删除失败', { type: 'error' });
            }
        }).catch((err: any) => {
            console.log(err);
            message('删除失败', { type: 'error' });
        });
    }

    return {
        columns,
        search_columns,
        mailno_select,
        pagination,
        loadingConfig,
        dataList,
        loading,
        tableSize,
        handleChange,
        handleSearch,
        handleRest,
        onSizeChange,
        onCurrentChange,
        handleSelectionChange,
        exportExcel,
        getMailNoData,
        getMailNoDataMulti,
        onCheckClick,
        onStatusClick,
        onDeleteClick
    };
}
