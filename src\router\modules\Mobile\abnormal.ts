let flag = navigator.userAgent.match(
    /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i
);
export default {
    path: "/abnormal",
    // redirect: "/abnormalTable",
    component: () => import("@/views/Mobile/abnormal/index.vue"),
    meta: {
        icon: "ri:file-close-line",
        title: "异常邮件",
        rank: 15
    }
} satisfies RouteConfigsTable;
