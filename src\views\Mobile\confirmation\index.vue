<script setup lang="ts">
import "plus-pro-components/es/components/search/style/css";
import { reactive, toRaw, ref, onMounted, getCurrentInstance } from "vue";
import { useColumns } from "./index";

const {
    searchValue,
    searchRef,
    V_AUDIT_TYPE,
    formData,
    show,
    onSearch,
    onSubmit

} = useColumns();

onMounted(() => {
    searchRef.value && searchRef.value.focus();
})

// getMine().then(res => {
//     console.log(res.data);

// });

</script>
<template>
    <div class="Confirmation">
        <header>
            <van-search v-model="searchValue" shape="round" background="#1989fa" placeholder="请扫描邮袋号" @search="onSearch"
                ref="searchRef" />
        </header>
        <main>
            <van-cell-group>
                <van-field v-model="formData.V_AUDIT_NO" label="序列号" disabled />
                <van-field v-model="V_AUDIT_TYPE" label="类型" disabled />
                <van-field v-model="formData.V_LOCK_NO" label="关锁号" required v-show="show" />
                <van-field v-model="formData.V_DRIVER_NO" label="司机纸号" required v-show="show" />
                <van-field v-model="formData.V_OPERNAME" name="操作人" label="操作人" disabled />
            </van-cell-group>
        </main>
        <div style="margin: 16px;">
            <van-button type="primary" block @click="onSubmit" round>装车确认</van-button>
        </div>
    </div>
</template>

<style scoped>
main {
    padding-top: 2vh;
    padding-bottom: 50px;
}
</style>