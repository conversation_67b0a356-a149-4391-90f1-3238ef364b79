<script setup lang="ts">
import { onMounted } from "vue";
import dayjs from 'dayjs';
import "plus-pro-components/es/components/search/style/css";
import { type PlusColumn, PlusSearch } from "plus-pro-components";
import { useColumns } from "./index";

const {
  columns,
  search_columns,
  mailno_select,
  pagination,
  loadingConfig,
  dataList,
  loading,
  tableSize,
  handleChange,
  handleSearch,
  handleRest,
  onSizeChange,
  onCurrentChange,
  handleSelectionChange,
  exportExcel,
  getMailNoData,
  onCheckClick,
  onStatusClick,
  onDeleteClick
} = useColumns();





onMounted(() => {
  // 修改前
  // getMailNoData(mailno_select).then((res: any) => {
  //   dataList.value = res;
  //   pagination.total = dataList.value.length;
  //   loading.value = false;
  // })

  // 修改后：直接调用 handleSearch
  handleSearch(mailno_select.value);
});



</script>

<template>
  <div>
    <PlusSearch
      v-model="mailno_select"
      :columns="search_columns"
      :show-number="10"
      label-width="120"
      :col-props="{ span: 8 }"
      label-position="right"
      @change="handleChange"
      @search="handleSearch"
      search-text="查询"
      @reset="handleRest"
      reset-text="重置"
    />
    <br />
    <div class="float-left">
      <el-button class="mb-[20px]" @click="onCheckClick">查验</el-button>
      <el-button class="mb-[20px]" @click="onStatusClick">状态</el-button>
    </div>
    <el-button type="primary" class="mb-[20px] float-right" @click="exportExcel">导出</el-button>
    <pure-table 
      :data="dataList.slice(
        (pagination.currentPage - 1) * pagination.pageSize,
        pagination.currentPage * pagination.pageSize
      )" 
      :columns="columns" 
      border 
      alignWhole="center" 
      :height="tableSize === 'small' ? 352 : 460"
      :pagination="pagination" 
      ref="waterRef" 
      @page-size-change="onSizeChange" 
      @page-current-change="onCurrentChange"
      @selection-change="handleSelectionChange">
      <template #operation="{ row }">
        <!-- 修改删除按钮的显示逻辑 -->
        <el-popconfirm
          v-if="!row.V_BAGNO"
          :title="`确定要删除邮件号 ${row.V_MAILNO} 吗?`"
          confirm-button-text="确定"
          cancel-button-text="取消"
          @confirm="onDeleteClick(row)"
        >
          <template #reference>
            <el-button link type="danger" size="small">
              删除
            </el-button>
          </template>
        </el-popconfirm>
        <!-- 当邮件已装袋时显示禁用状态 -->
        <el-tooltip
          v-else
          content="已装袋邮件不可删除"
          placement="top"
        >
          <el-button link type="danger" size="small" disabled>
            删除
          </el-button>
        </el-tooltip>
      </template>
    </pure-table>
  </div>
</template>

<style lang="scss" scoped>
:deep(.plus-search) {
    .el-form-item {
        margin-bottom: 18px;
    }

    .el-date-editor {
        width: 100% !important;
    }

    .el-range-editor {
        .el-range-input {
            width: 42% !important;
        }
    }

    .el-form-item__label {
        white-space: nowrap;
    }
}
</style>
