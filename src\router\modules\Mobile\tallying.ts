let flag = navigator.userAgent.match(
    /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i
);
export default {
    path: "/tallying",
    // redirect: "/abnormalTable",
    component: () => import("@/views/Mobile/tallying/index.vue"),
    meta: {
        icon: "fluent-mdl2:gather",
        // showLink: flag ? true : false,
        title: "理货采集",
        rank: 11
    }
} satisfies RouteConfigsTable;
