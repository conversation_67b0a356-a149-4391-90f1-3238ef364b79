let flag = navigator.userAgent.match(
    /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i
);
export default {
    path: "/tarfficscan",
    // redirect: "/abnormalTable",
    component: () => import("@/views/Mobile/tarfficscan/index.vue"),
    meta: {
        icon: "ri:plane-line",
        // showLink: flag ? true : false,
        title: "退运交航",
        rank: 16
    }
} satisfies RouteConfigsTable;
