import { clone, delay } from "@pureadmin/utils";
import { ref, onMounted, reactive, watchEffect, toRaw } from "vue";
import type { PaginationProps, AdaptiveConfig, LoadingConfig, Align } from "@pureadmin/table";
import { utils, writeFile } from "xlsx";
import { message } from "@/utils/message";
import { axios_gh, axios_hg } from "@/api/user";
import "plus-pro-components/es/components/search/style/css";
import { type PlusColumn, PlusSearch } from "plus-pro-components";
import router from "@/router";
import Cookies from "js-cookie";

export function useColumns() {

    const dataList = ref([]);
    const loading = ref(true);
    const select = ref("no");
    const hideVal = ref("nohide");
    const tableSize = ref("default");
    const paginationSmall = ref(false);
    const paginationAlign = ref("right");
    const carList = reactive([])

    let columns: TableColumnList = [
        {
            type: "selection",
            align: "left"
        },
        {
            label: "车牌号",
            prop: "V_CARNO",
            width: 140,
        },
        {
            label: "企业名称",
            prop: "V_ENT_NAME",
            width: 140
        },
        {
            label: '类型',
            prop: 'V_ENT_TYPE',
            width: 140,
            cellRenderer: ({ row }) => {
                switch (row.V_ENT_TYPE) {
                    case '1':
                        return '转场';
                    case '2':
                        return '转关';
                    case '3':
                        return '转运';
                    default:
                        return '';
                }

            }
        },
        {
            label: '操作人',
            prop: 'V_OPERNAME',
        },
        {
            label: '审核情况',
            prop: 'V_CHECK',
            cellRenderer: ({ row }) => {
                switch (row.V_CHECK) {
                    case '1':
                        return '审核通过';
                    case '0':
                        return '审核未通过';
                    case '-1':
                        return '未进行审核';
                    default:
                        return '';
                }

            }
        },
        {
            label: '说明',
            prop: 'V_PASSDESC',
        },
        {
            label: '当前状态',
            prop: 'V_STATUS',
        },
        {
            label: "操作",
            width: "120",
            fixed: "right",
            slot: "operation"
        }
    ];

    let search_columns: PlusColumn[] = [
        {
            label: "车牌号",
            prop: "V_CARNO",
            // tooltip: "显示6个字符",
            fieldProps: {
                placeholder: "请输入车牌号"
            }
        },
        {
            label: "类型",
            prop: "V_ENT_TYPE",
            valueType: "select",
            fieldProps: {
                placeholder: "全部"
            },
            options: [
                {
                    label: "全部",
                    value: "",
                },
                {
                    label: "转场",
                    value: "1",
                },
                {
                    label: "转关",
                    value: "2",
                },
                {
                    label: "装运",
                    value: "3",
                }
            ]
        },
        {
            label: "审核情况",
            prop: "V_CHECK",
            valueType: "select",
            fieldProps: {
                placeholder: "全部"
            },
            options: [
                {
                    label: "全部",
                    value: "",
                },
                {
                    label: "审核通过",
                    value: "1",
                },
                {
                    label: "审核未通过",
                    value: "0",
                },
                {
                    label: "未进行审核",
                    value: "-1",
                }
            ]
        },
        {
            label: "操作人",
            prop: "V_OPERNAME",
            fieldProps: {
                placeholder: "请输入操作人"
            }
        },
    ];


    // let tableData = [];
    let multipleSelection = ref([]);

    /** 分页配置 */
    const pagination = reactive<PaginationProps>({
        pageSize: 10,
        currentPage: 1,
        pageSizes: [10, 50, 200, 500, 1000],
        total: 0,
        align: "center",
        background: true,
        small: false
    });

    /** 加载动画配置 */
    const loadingConfig = reactive<LoadingConfig>({
        text: "正在加载第一页...",
        viewBox: "-10, -10, 50, 50",
        spinner: `
        <path class="path" d="
            M 30 15
            L 28 17
            M 25.61 25.61
            A 15 15, 0, 0, 1, 15 30
            A 15 15, 0, 1, 1, 27.99 7.5
            L 15 15
        " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>
        `
        // svg: "",
        // background: rgba()
    });

    /** 撑满内容区自适应高度相关配置 */
    const adaptiveConfig: AdaptiveConfig = {
        /** 表格距离页面底部的偏移量，默认值为 `96` */
        offsetBottom: 110
        /** 是否固定表头，默认值为 `true`（如果不想固定表头，fixHeader设置为false并且表格要设置table-layout="auto"） */
        // fixHeader: true
        /** 页面 `resize` 时的防抖时间，默认值为 `60` ms */
        // timeout: 60
        /** 表头的 `z-index`，默认值为 `100` */
        // zIndex: 100
    };

    function onChange(val) {
        pagination.small = val;
    }

    function onSizeChange(val) {
        console.log("onSizeChange", val);
    }

    function onCurrentChange(val) {
        loadingConfig.text = `正在加载第${val}页...`;
        loading.value = true;
        delay(600).then(() => {
            loading.value = false;
        });
    }

    const exportExcel = () => {
        const res = dataList.value.map(item => {
            const arr = [];
            columns.forEach(column => {
                arr.push(item[column.prop as string]);
            });
            return arr;
        });
        const titleList = [];
        columns.forEach(column => {
            titleList.push(column.label);
        });
        res.unshift(titleList);
        const workSheet = utils.aoa_to_sheet(res);
        const workBook = utils.book_new();
        utils.book_append_sheet(workBook, workSheet, "车辆备案");
        writeFile(workBook, "车辆备案表" + new Date().getTime() + ".xlsx");
        message("导出成功", {
            type: "success"
        });
    };

    const handleSelectionChange = val => {
        multipleSelection.value = val;
    };

    // watchEffect(() => {
    //     pagination.align = paginationAlign.value as Align;
    // });

    //默认查询数据
    let filings_select = ref({
        V_CARNO: '',
        V_OPERNAME: '',
        V_ENT_TYPE: '',
        V_CHECK: ''
    });


    const handleChange = (values: any) => {
        console.log(values, "change");
    };
    const handleSearch = (values: any) => {
        getCarListTable(toRaw(values)).then((res) => {
            dataList.value = res;
            pagination.total = dataList.value.length;
            loading.value = false;
            message('查询成功', { type: 'success' })
        });
    };
    const handleRest = () => {
        filings_select = ref({
            V_CARNO: '',
            V_OPERNAME: '',
            V_ENT_TYPE: '',
            V_CHECK: ''
        });
        // message('重置成功', { type: 'success' })
        handleSearch(filings_select.value)
    };
    //获取数据
    function getCarListTable(object?: any) {
        return axios_gh('/clxxcx', object).then((res: any) => {
            let data = res.data
            for (let i = 0; i < data.length; i++) {
                data[i]["key"] = i
            }
            return data
        })
    }

    /**
     * 审核情况
     */

    //查询车辆审核情况并刷新
    function onCheckClik() {
        let data_array = toRaw(multipleSelection.value);
        let len = data_array.length;
        if (len > 0) {
            let list = []
            for (let i = 0; i < len; i++) {
                let carno = data_array[i].V_CARNO;
                // console.log(data_array[i].V_CARNO);
                list.push(CarResultReport(carno))
            }
            Promise.all(list).then((res: any) => {
                message('查询成功', { type: 'success' })
                handleSearch(filings_select.value)
            })
        } else {
            message('请选择车辆', { type: 'error' })
        }
    }

    //海关-车辆审核结果查询
    function CarResultReport(carNo: string) {
        let params = []
        params.push(carNo)
        let new_params = (JSON.stringify(params)).replace(/\"/g, "'")
        return axios_hg({
            service: 'RetpostMailService',
            method: 'CarResultReport',
            params: new_params
        }).then((res: any) => {
            let data = res.result.info[0]
            updateCarCheck(carNo, data)
        }).catch((err: any) => {
            console.log(err);
            message('(海关接口)审核结果查询失败, 原因：' + err, { type: 'error' })
            // let object = { carNO: carNo, reason: err }
            // warningInfo.value.push(object)
        })
    }

    //审核结果更新数据库
    function updateCarCheck(carNo: string, checkInfo: any) {
        return axios_gh('/clxxbashjggx', {
            V_CARNO: carNo,
            V_CHECK: checkInfo.check,
            V_PASSDESC: checkInfo.passdesc || checkInfo.desc
        })
    }


    /**
     * 删除
     */

    function onDeleteClick(row) {
        console.log(row);
        CarDeleteReport(row.V_CARNO, row.key)
    }
    //海关-车辆信息备案删除
    function CarDeleteReport(carNo: string, index: number) {
        let params = [Cookies.get("USER_NAME")]
        params.unshift(carNo)
        let new_params = (JSON.stringify(params)).replace(/\"/g, "'")

        axios_hg({
            service: 'RetpostMailService',
            method: 'CarDeleteReport',
            params: new_params
        }).then((res: any) => {
            let data = res.result.info[0]
            if (data.flag == '1') {
                // carList.splice(index, 1)
                deleteCarNo(carNo)
                message('(海关接口)删除成功', { type: 'success' })
            } else {
                message('(海关接口)删除失败', { type: 'error' })
            }
        }).catch((err: any) => {
            console.log(err);
            message('(海关接口)车辆信息备案删除失败,原因：' + err, { type: 'error' })
        })
    }

    //数据库删除车辆
    function deleteCarNo(carNo: string) {
        axios_gh('/clxxsc', {
            V_CARNO: carNo
        })
    }


    function onAddClick() {
        router.push({
            path: '/filings/add'
        })

    }

    function onUpdateClick(row) {
        router.push({
            path: '/filings/update',
            query: {
                carno: row.V_CARNO,
                type: row.V_ENT_TYPE
            }
        })
    }



    return {
        columns,
        search_columns,
        filings_select,
        pagination,
        loadingConfig,
        dataList,
        loading,
        tableSize,
        // tableData,
        handleChange,
        handleSearch,
        handleRest,
        onSizeChange,
        onCurrentChange,
        handleSelectionChange,
        exportExcel,
        getCarListTable,
        onAddClick,
        onDeleteClick,
        onCheckClik,
        onUpdateClick
    };
}