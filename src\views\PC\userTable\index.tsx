import { clone, delay } from "@pureadmin/utils";
import { ref, onMounted, reactive, watchEffect, toRaw } from "vue";
import type { PaginationProps, AdaptiveConfig, LoadingConfig, Align } from "@pureadmin/table";
import { utils, writeFile } from "xlsx";
import { message } from "@/utils/message";
import { axios_gh } from "@/api/user";
import "plus-pro-components/es/components/search/style/css";
import { type PlusColumn, PlusSearch } from "plus-pro-components";
import router from "@/router";
import md5 from 'js-md5';
export function useColumns() {

    let columns: TableColumnList = [
        // {
        //     type: "selection",
        //     align: "left",
        //     // fixed: true
        // },
        {
            label: "员工ID",
            prop: "V_USER_ID",
            // fixed: true
        },
        {
            label: "员工名",
            prop: "V_USER_NAME",
            // fixed: true
        },
        {
            label: "账号",
            prop: "V_LOGIN_CODE",
        },
        {
            label: "用户角色",
            prop: "ROLE_NAME",
        },
        {
            label: "操作",
            width: "160",
            slot: "operation"
        }

    ];

    let search_columns: PlusColumn[] = [
        {
            label: "员工名",
            prop: "V_USER_NAME",
            // tooltip: "显示6个字符",
            fieldProps: {
                placeholder: "请输入员工号"
            }
        },
        {
            label: "账号",
            prop: "V_LOGIN_CODE",
            fieldProps: {
                placeholder: "请输入账号"
            }
        },
    ];

    const dataList = ref([]);
    const loading = ref(true);
    const tableSize = ref("default");
    let multipleSelection = ref([]);
    // let tableData = [];

    //默认查询数据
    let user_select = ref({
        V_USER_NAME: '',
        V_LOGIN_CODE: '',
    });



    /** 分页配置 */
    const pagination = reactive<PaginationProps>({
        pageSize: 10,
        currentPage: 1,
        pageSizes: [10, 50, 200, 500, 1000],
        total: 0,
        align: "center",
        background: true,
        small: false
    });


    /** 加载动画配置 */
    const loadingConfig = reactive<LoadingConfig>({
        text: "正在加载第一页...",
        viewBox: "-10, -10, 50, 50",
        spinner: `
    <path class="path" d="
        M 30 15
        L 28 17
        M 25.61 25.61
        A 15 15, 0, 0, 1, 15 30
        A 15 15, 0, 1, 1, 27.99 7.5
        L 15 15
    " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>
    `
        // svg: "",
        // background: rgba()
    });

    const handleChange = (values: any) => {
        console.log(values, "change");
    };
    //查询
    const handleSearch = (values: any) => {

        getUserData(toRaw(values)).then((res) => {

            dataList.value = res;
            pagination.total = dataList.value.length;
            loading.value = false;
            message('查询成功', { type: 'success' })
        });
    };

    function onSizeChange(val: number) {
        message('更改为每页' + val + '条', { type: 'success', offset: 500 });
    }

    function onCurrentChange(val: number) {
        // console.log(val);

        // loadingConfig.text = `正在加载第${val}页...`;
        // loading.value = true;
        // delay(600).then(() => {
        //     loading.value = false;
        // });
    }

    const handleSelectionChange = (val: any) => {
        multipleSelection.value = val;
    };

    // 重置
    const handleRest = () => {
        user_select = ref({
            V_USER_NAME: '',
            V_LOGIN_CODE: '',
        });
        handleSearch(user_select.value)
    };

    const exportExcel = () => {
        const res = dataList.value.map(item => {
            const arr = [];
            columns.forEach(column => {
                arr.push(item[column.prop as string]);
            });
            return arr;
        });
        const titleList = [];
        columns.forEach(column => {
            titleList.push(column.label);
        });
        res.unshift(titleList);
        const workSheet = utils.aoa_to_sheet(res);
        const workBook = utils.book_new();
        utils.book_append_sheet(workBook, workSheet, "用户管理");
        writeFile(workBook, "用户管理表" + new Date().getTime() + ".xlsx");
        message("导出成功", {
            type: "success"
        });
    };

    //获取员工数据
    const getUserData = (object?: any) => {
        return axios_gh('/cxyhlb', object).then((res: any) => {
            let data = res.data
            for (let i = 0; i < data.length; i++) {
                data[i]["key"] = i
            }
            // tableData = []
            // tableData = data
            return data
            // message('查询成功', { type: 'success' })
        })
    }
    //重置密码
    function onResetPswClick(val: any) {
        console.log(val);
        //默认密码
        let resetpsw = md5('123456').toUpperCase();
        axios_gh('/resetpsw',
            {
                V_USER_ID: val.V_USER_ID,
                V_RESET_PSW: resetpsw
            }
        ).then((res: any) => {
            message('重置成功', { type: 'success' })
        }).catch((err: any) => {
            message('重置失败', { type: 'error' })
        })
    }

    function onDeleteClick(val: any) {
        axios_gh('/scyh', { V_USER_ID: val.V_USER_ID }).then((res: any) => {
            message('删除成功', { type: 'success' })
            handleSearch(user_select.value)
        }).catch((err: any) => {
            message('删除失败', { type: 'error' })
        })
    }

    function onAddClick() {
        router.push({
            path: '/userTable/add'
        })
    }



    return {
        columns,
        search_columns,
        user_select,
        pagination,
        loadingConfig,
        dataList,
        loading,
        tableSize,
        // tableData,
        handleChange,
        handleSearch,
        handleRest,
        onSizeChange,
        onCurrentChange,
        handleSelectionChange,
        exportExcel,
        getUserData,
        onAddClick,
        onResetPswClick,
        onDeleteClick
    };
}
