import { clone, delay } from "@pureadmin/utils";
import { ref, onMounted, reactive, watchEffect, toRaw } from "vue";
import type { PaginationProps, AdaptiveConfig, LoadingConfig, Align } from "@pureadmin/table";
import { utils, writeFile } from "xlsx";
import { message } from "@/utils/message";
import { axios_gh, axios_hg, axios_gh1 } from "@/api/user";
import "plus-pro-components/es/components/search/style/css";
import { type PlusColumn, PlusSearch } from "plus-pro-components";
import { addDialog } from "@/components/ReDialog";
import { IconifyIconOffline, IconifyIconOnline } from "@/components/ReIcon";
import Cookies from "js-cookie";
import dayjs from 'dayjs';
import MultiBagnoInput from "@/components/MultiBagnoInput/index.vue"; // 导入自定义组件

export function useColumns() {

    const dataList = ref([]);
    const loading = ref(true);
    const select = ref("no");
    const hideVal = ref("nohide");
    const tableSize = ref("default");
    const paginationSmall = ref(false);
    const paginationAlign = ref("right");
    let tableData = [];
    let multipleSelection = ref([]);


    let columns: TableColumnList = [
        {
            type: "selection",
            align: "left",
            fixed: true
        },
        {
            label: "邮袋号",
            prop: "V_BAGNO",
            width: 300,
            fixed: true
        },
        {
            label: "绑定失败原因",
            prop: "V_FAILREASON",
            width: 160,
            cellRenderer: ({ row }) => {
                if (!row.V_FAILREASON) {
                    return <span>-</span>;
                }
                
                return (
                    <div class="failure-reason">
                        <el-tooltip
                            placement="top"
                            width="400"
                            trigger="hover"
                        >
                            {{
                                default: () => (
                                    <div class="truncate-text" style="max-width: 150px;">
                                        {row.V_FAILREASON}
                                    </div>
                                ),
                                content: () => (
                                    <div>
                                        <div style="max-width: 400px; word-wrap: break-word;">
                                            {row.V_FAILREASON}
                                        </div>
                                        <el-button
                                            link
                                            type="primary"
                                            onClick={(e: Event) => {
                                                e.stopPropagation();
                                                copyToClipboard(row.V_FAILREASON);
                                            }}
                                        >
                                            复制
                                        </el-button>
                                    </div>
                                )
                            }}
                        </el-tooltip>
                    </div>
                );
            }
        },
        {
            label: "重量(kg)",
            prop: "N_BAGWEIGHT",
            width: 80
        },
        {
            label: '目的地',
            prop: 'V_DESTINATION',
            width: 160
        },
        {
            label: '航班号',
            prop: 'V_HBH',
            width: 120
        },
        {
            label: '封发时间',
            prop: 'D_DESPTIME',
            width: 110
        },
        {
            label: '出库装车序列号',
            prop: 'V_AUDIT_NO',
            width: 170
        },
        {
            label: '采集时间',
            prop: 'D_CJSJ',
            width: 110
        },
        {
            label: '操作人',
            prop: 'V_OPERATORNAME',
        },
        {
            label: '存储位置',
            prop: 'V_STORAGELOCATION',
            cellRenderer: ({ row }) => (
                <a style="color: #409eff; font-size: 18px;" onClick={() => onStorageLocationClick(row)}>
                    {row.V_STORAGELOCATION}
                </a>
            )
        },
      
        {
            label: '核查情况',
            prop: 'V_CHECK',
            width: 130,
            cellRenderer: ({ row }) => {
                switch (row.V_CHECK) {
                    case '命中核查':
                        return <div class="cell" style="color:#f56c6c; font-weight: bold;">命中核查
                            {/* <IconifyIconOnline icon="pajamas:clear" width="18px" color="#f56c6c" /> */}
                        </div>;
                    case '未命中核查':
                        return <div class="cell" style="color:#409eff">未命中核查</div>;
                    case '绑定失败':
                        return '绑定失败';
                    default:
                        return <div class="cell" style="color:#909399">未绑定
                            {/* <IconifyIconOnline icon="pajamas:severity-unknown" width="18px" color="#909399" /> */}
                        </div>;
                }

            }
        },
        {
            label: '邮袋当前状态',
            prop: 'V_MAILBAGSTATUS',
            width: 160,
            cellRenderer: ({ row }) => {
                switch (row.V_MAILBAGSTATUS) {
                    case '邮袋不存在':
                        return <el-tag type="danger" effect="dark">
                            {row.V_MAILBAGSTATUS}
                        </el-tag>;
                    case '邮袋装车出库':
                        return <el-tag type="primary" effect="dark">
                            {row.V_MAILBAGSTATUS}
                        </el-tag>;
                    case '跟装车出库申请绑定':
                        return <el-tag type="warning" effect="dark">
                            {row.V_MAILBAGSTATUS}
                        </el-tag>;
                    case '退运邮件已装袋':
                        return <el-tag type="success" effect="dark">
                            {row.V_MAILBAGSTATUS}
                        </el-tag>;
                    default:
                        return <el-tag type="info">
                            {row.V_MAILBAGSTATUS}
                        </el-tag>;
                }
            }
        },
        {
            label: '邮件',
            prop: 'V_MAIL_NUM',
            cellRenderer: ({ row }) => {
                if (row.V_MAIL_NUM > 0) {
                    return (<a style="color: #409eff; font-size: 18px;" onClick={() => showDetailMailData(row)}>
                        {row.V_MAIL_NUM}
                    </a>);
                } else {
                    return <a style="color: #409eff; font-size: 18px;">{row.V_MAIL_NUM}</a>
                }
            }
            // (
            //     <a style="color: #409eff; font-size: 18px;" onClick={() => showDetailMailData(row)}>
            //         {row.V_MAIL_NUM}
            //     </a>
            // )
        },
        {
            label: '交航扫描结果',
            prop: 'V_MAILBAGRET',
            width: 120
        },
        {
            label: "操作",
            width: "120",
            fixed: "right",
            slot: "operation"
        }

    ];

    let search_columns: PlusColumn[] = [
        {
            label: "邮袋号",
            prop: "V_BAGNO",
            // 使用自定义组件
            component: MultiBagnoInput,
            componentProps: {
                placeholder: "请输入邮袋号，支持批量",
                rows: 3
            },
            fieldProps: {
                placeholder: "支持批量"
            }
        },
        {
            label: "出库序列号",
            prop: "V_AUDIT_NO",
            fieldProps: {
                placeholder: "请输入出库装车序列号",
            },
        },
        {
            label: "采集时间",
            prop: "D_CJSJ",
            valueType: "date-picker",
            fieldProps: {
                type: "daterange",
                startPlaceholder: "开始时间",
                endPlaceholder: "结束时间",
                valueFormat: "YYYY/MM/DD"
            }
        },
        {
            label: "核查情况",
            prop: "V_CHECK",
            valueType: "select",
            fieldProps: {
                placeholder: "全部",
                style: {
                    width: '100%'
                },
                popperClass: 'custom-select-dropdown'
            },
            options: [
                {
                    label: "全部",
                    value: "",
                },
                {
                    label: "命中核查",
                    value: "1",
                },
                {
                    label: "未命中核查",
                    value: "0",
                },
                {
                    label: "绑定失败",
                    value: "-1",
                }
            ]
        },
        {
            label: "邮袋状态",
            prop: "V_MAILBAGSTATUS",
            valueType: "select",
            fieldProps: {
                placeholder: "全部",
                style: {
                    width: '100%'
                },
                popperClass: 'custom-select-dropdown'
            },
            options: [
                {
                    label: "全部",
                    value: "",
                },
                {
                    label: "退运邮件已装袋",
                    value: "退运邮件已装袋",
                },
                {
                    label: "跟装车出库申请绑定",
                    value: "跟装车出库申请绑定",
                },
                {
                    label: "邮袋装车出库",
                    value: "邮袋装车出库",
                }
            ]
        },
        {
            label: "邮件号",
            prop: "V_MAILNO",
            fieldProps: {
                placeholder: "请输入邮件号"
            }
        },
        {
            label: "航班号",
            prop: "V_HBH",
            fieldProps: {
                placeholder: "请输入航班号"
            }
        },
        {
            label: "存储位置",
            prop: "V_STORAGELOCATION",
            fieldProps: {
                placeholder: "请输入储位号"
            }
        },
    ];

    /** 分页配置 */
    const pagination = reactive<PaginationProps>({
        pageSize: 10,
        currentPage: 1,
        pageSizes: [10, 50, 200, 500, 1000],
        total: 0,
        align: "center",
        background: true,
        small: false
    });


    /** 加载动画配置 */
    const loadingConfig = reactive<LoadingConfig>({
        text: "正在加载第一页...",
        viewBox: "-10, -10, 50, 50",
        spinner: `
    <path class="path" d="
        M 30 15
        L 28 17
        M 25.61 25.61
        A 15 15, 0, 0, 1, 15 30
        A 15 15, 0, 1, 1, 27.99 7.5
        L 15 15
    " style="stroke-width: 4px; fill: rgba(0, 0, 0, 0)"/>
    `
        // svg: "",
        // background: rgba()
    });
    

    // 处理存储位置点击事件
    function onStorageLocationClick(row) {
        // 这里可以实现修改存储位置的逻辑
        console.log("点击了存储位置:", row.V_STORAGELOCATION);
        // 例如弹出一个对话框让用户输入新的存储位置
        addDialog({
            title: '修改存储位置',
            contentRenderer: () => (
                <div>
                    <input v-model={row.V_STORAGELOCATION} placeholder="请输入新的存储位置" />
                </div>
            ),
            closeCallBack: ({ options, index, args }) => {
                if (args?.command === 'sure') {
                    // 更新存储位置
                    updateStorageLocation(row.V_BAGNO, row.V_STORAGELOCATION).then(() => {
                        message('存储位置修改成功', { type: 'success' });
                    }).catch((err) => {
                        message('存储位置修改失败: ' + err, { type: 'error' });
                    });
                }
            }
        });
    }

    // 更新存储位置的函数
    function updateStorageLocation(bagno: string, newLocation: string) {
        return axios_gh('/updateStorageLocation', {
            V_BAGNO: bagno,
            V_STORAGELOCATION: newLocation
        }).then((res: any) => {
            if (res.success) {
                return res;
            } else {
                throw new Error(res.message);
            }
        }).catch((err: any) => {
            console.error('Error updating storage location:', err);
            throw err;
        });
    }


    function onSizeChange(val: number) {
        message('更改为每页' + val + '条', { type: 'success', offset: 500 });
    }

    function onCurrentChange(val: number) {
        // console.log(val);

        // loadingConfig.text = `正在加载第${val}页...`;
        // loading.value = true;
        // delay(600).then(() => {
        //     loading.value = false;
        // });
    }

    const exportExcel = () => {
        //导入数据
        const res = dataList.value.map(item => {
            const arr = [];
            columns.forEach((column, index) => {
                if (index > 0) {
                    arr.push(item[column.prop as string]);
                }
            });
            return arr;
        });
        //导入标题
        const titleList = [];
        columns.forEach((column, index) => {
            if (index > 0) {
                titleList.push(column.label);
            }
        });
        res.unshift(titleList);
        const workSheet = utils.aoa_to_sheet(res);

        //设置列宽
        workSheet[`!cols`] = [
            { wpx: 230 },
            { wpx: 80 },
            { wpx: 80 },
            { wpx: 80 },
            { wpx: 80 },
            { wpx: 150 },
            { wpx: 80 },
            { wpx: 80 },
            { wpx: 80 },
            { wpx: 120 },
            { wpx: 80 },
            { wpx: 80 },
        ]

        const workBook = utils.book_new();
        utils.book_append_sheet(workBook, workSheet, "理货总包");
        writeFile(workBook, "理货总包表" + new Date().getTime() + ".xlsx");
        message("导出成功", {
            type: "success"
        });
    };

    const handleSelectionChange = val => {
        multipleSelection.value = val;
    };

    //默认查询数据
    let tallying_select = ref({
        V_BAGNO: '',
        V_AUDIT_NO: '',
        D_CJSJ: [
            dayjs().format('YYYY/MM/DD'),  // 今天开始
            dayjs().format('YYYY/MM/DD')   // 今天结束
        ],
        V_CHECK: '',
        V_MAILBAGSTATUS: '',
        V_MAILNO: '',
        V_HBH: '',
        V_STORAGELOCATION: ''
    });

    onMounted(() => {
        handleSearch(tallying_select.value);
    });

    const handleChange = (values: any) => {
        console.log(values, "change");
    };
    const handleSearch = (values: any) => {
        // 阻止默认事件，防止页面跳转
        if (event) {
            event.preventDefault?.();
        }
        
        // 处理邮袋号，将文本框中的多行文本转换为数组
        const bagnoValue = values.V_BAGNO || '';
        const bagnos = bagnoValue.split(/[\n,，\s]/).map(item => item.trim()).filter(Boolean);

        // 如果只有一个邮袋号，使用单个查询接口
        if (bagnos.length <= 1) {
            const searchParams = {
                ...values,
                V_BAGNO: bagnos[0] || '',
                D_CJKSSJ: values.D_CJSJ?.[0] || '',
                D_CJJSSJ: values.D_CJSJ?.[1] || '',
            };
            delete searchParams.D_CJSJ;

            getTallyingData(toRaw(searchParams)).then((res: any) => {
                dataList.value = res;
                pagination.total = dataList.value.length;
                loading.value = false;
                message('查询成功', { type: 'success' });
            });
        } 
        // 如果有多个邮袋号，使用多邮袋号查询接口
        else {
            // 创建一个新对象，避免修改原始值
            const searchParams = new URLSearchParams();
            
            // 添加基本参数
            Object.entries(toRaw(values)).forEach(([key, value]) => {
                if (key !== 'V_BAGNO' && key !== 'D_CJSJ') {
                    searchParams.append(key, value as string);
                }
            });
            
            // 添加日期参数
            searchParams.append('D_CJKSSJ', values.D_CJSJ?.[0] || '');
            searchParams.append('D_CJJSSJ', values.D_CJSJ?.[1] || '');
            
            // 添加多个邮袋号，每个作为单独的参数
            bagnos.forEach(bagno => {
                searchParams.append('V_BAGNOS', bagno);
            });
            
            // 使用 axios 发送请求
            axios_gh('/selectMulti', searchParams, {
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            }).then((res: any) => {
                let data = res.data;
                for (let i = 0; i < data.length; i++) {
                    data[i]["key"] = i;
                }
                dataList.value = data;
                pagination.total = dataList.value.length;
                loading.value = false;
                message('查询成功', { type: 'success' });
            }).catch(err => {
                console.error('查询失败:', err);
                message('查询失败', { type: 'error' });
                loading.value = false;
            });
        }
    };
    const handleRest = () => {
        tallying_select.value = {
            V_BAGNO: '',
            V_AUDIT_NO: '',
            D_CJSJ: [
                dayjs().format('YYYY/MM/DD'),  // 今天开始
                dayjs().format('YYYY/MM/DD')   // 今天结束
            ],
            V_CHECK: '',
            V_MAILBAGSTATUS: '',
            V_MAILNO: '',
            V_HBH: '',
            V_STORAGELOCATION: ''
        };
        handleSearch(tallying_select.value);
    };

    //获取邮袋数据
    async function getTallyingData(object?: any) {
        return axios_gh('/select', object).then((res: any) => {
            let data = res.data
            for (let i = 0; i < data.length; i++) {
                data[i]["key"] = i
            }
            return data
        })
    }

    //获取邮袋数据（多邮袋号查询）
    async function getTallyingDataMulti(object?: any) {
        return axios_gh('/selectMulti', object).then((res: any) => {
            let data = res.data
            for (let i = 0; i < data.length; i++) {
                data[i]["key"] = i
            }
            return data
        })
    }

    /**
     * 推送
     */
    //已经爬取，但未理货的数据才会推送
    function onPushClick() {
        axios_gh('/lhydts').then((res: any) => {
            let data = res.data
            let len = data.length
            if (len > 0) {
                addDialog({
                    title: '推送',
                    contentRenderer: () => <p>{data.length} 个邮袋需要推送,</p>,
                    closeCallBack: ({ options, index, args }) => {
                        if (args?.command === 'sure') {
                            for (let i = 0; i < data.length; i++) {
                                mailbagReport(data[i])
                            }
                        }
                    },
                })
            } else {
                message('当前没有数据需要推送', { type: 'success' })
            }
        })
    }

    //海关-邮袋推送，理货申报接口
    function mailbagReport(pushData) {
        let params = []
        params.push(pushData.V_BAGNO)
        params.push(pushData.V_MAILNO)
        params.push(pushData.N_BAGWEIGHT + 'kg')
        params.push(pushData.V_DESTINATION)
        params.push(pushData.V_OPERATORNAME)
        let new_params = (JSON.stringify(params)).replace(/\"/g, "'")
        axios_hg({
            service: 'RetpostMailService',
            method: 'mailbagReport',
            params: new_params
        }).then((res: any) => {
            let data = res.result.info[0]
            let dataArray = data.mailbagRet.split('-')
            let failReason = ''
            if (dataArray[0] === 'fail') {
                failReason=data.failReason
            }
            updatefailreson(pushData.V_BAGNO, failReason)

            if (dataArray[0] == 'success') {
                message(dataArray[1] + '推送成功', { type: 'success' })
                updateFlag(pushData.V_BAGNO)
            } else {
                let reason = data.failReason
                message(dataArray[1] + '推送失败' + reason, { type: 'error' })
            }
        }).catch((err: any) => {
            console.log(err)
            message('(海关接口)出现错误，原因：' + err, { type: 'error' })
        })
    }

    //推送后修改标志
    function updateFlag(bagno: string) {
        return axios_gh('/tshxgbz', {
            V_BAGNO: bagno,
        })
    }

    /**
     * 状态
     */
    function onStatusClick() {
        let data_array = toRaw(multipleSelection.value);
        let len = data_array.length;
        if (len > 0) {
            for (let i = 0; i < len; i++) {
                //获取邮袋号
                let mailbagno = data_array[i].V_BAGNO
                mailbagQueryReport(mailbagno)
            }
        } else {
            message('请选择数据', { type: 'error' })
        }
    }


    //海关-邮袋信息查询接口
    function mailbagQueryReport(bagno: string) {
        let params = [bagno]
        let new_params = (JSON.stringify(params)).replace(/\"/g, "'")
        axios_hg({
            service: 'RetpostMailService',
            method: 'mailbagQueryReport',
            params: new_params
        }).then((res: any) => {
            let data = res.result.info[0]
            updateBagStatus(bagno, data.mailbagStatus)
            message(data.mailbagStatus, { type: 'success' })
        }).catch((err: any) => {
            console.log(err)
            message('(海关接口)出现错误，原因：' + err, { type: 'error' })
        })
    }


    //更新邮袋状态信息
    function updateBagStatus(bagno: string, bagStatus: string) {
        axios_gh('/updatebagstatus', {
            V_BAGNO: bagno,
            V_MAILBAGSTATUS: bagStatus
        }).then((res: any) => {
            handleSearch(tallying_select.value)
        }).catch((err: any) => {
            console.log(err)
            message('更新状态失败', { type: 'error' })
        })
    }

    //更新邮袋绑定失败信息
    function updatefailreson(bagno: string, failreason: string) {
        // 如果failreason是数组，转换为字符串
        let reasonStr = '';
        if (Array.isArray(failreason)) {
            reasonStr = failreason.join('; ');  // 用分号分隔
        } else {
            reasonStr = failreason || '';
        }
        axios_gh('/gxydbdsbyy', {
            V_BAGNO: bagno,
            V_FAILREASON: reasonStr
        }).then((res: any) => {
            //handleSearch(tallying_select.value)
        }).catch((err: any) => {
            console.log(err)
            message('更新原因失败', { type: 'error' })
        })
    }

    /**
     * 删除
     */
    function onDeleteClick(row) {
        mailbagDelReport(row.V_BAGNO).then((res: any) => {
            console.log(res);
            let data = res.result.info[0]
            let retArray = data.mailbagRet.split('-')
            let isSuccess = retArray[0]
            if (isSuccess == 'success') {
                message(retArray[1], { type: 'success' })
                // deleteCjzbtm(row.V_BAGNO)
                deleteCjzbtm(row.V_BAGNO).then(deleteResult => {
                    if (deleteResult) {
                        message('删除成功', { type: 'success' });
                    } else {
                        message('删除失败', { type: 'error' });
                    }
                });
            } else if (isSuccess == 'fail' && retArray[2] == '邮袋号不存在') { 
                message('海关' + retArray[2], { type: 'success' })
                // deleteCjzbtm(row.V_BAGNO)
                deleteCjzbtm(row.V_BAGNO).then(deleteResult => {
                    if (deleteResult) {
                        message('删除成功', { type: 'success' });
                    } else {
                        message('删除失败', { type: 'error' });
                    }
                });

            } else {
                message(retArray[1], { type: 'error' })
            }
        }).catch((err: any) => {
            message('(海关接口)出现错误，原因：' + err, { type: 'error' })
        })
    }


    //海关-邮袋删除申报接口
    function mailbagDelReport(bagno: string) {
        let params = [bagno, Cookies.get("USER_NAME")]
        let new_params = (JSON.stringify(params)).replace(/\"/g, "'")
        return axios_hg({
            service: 'RetpostMailService',
            method: 'mailbagDelReport',
            params: new_params
        })
    }

    // function deleteCjzbtm(bagno: string) {
    //     return axios_gh('/deleteCjzbtm', {
    //         V_BAGNO: bagno
    //     })
    // }
    
    // 删除邮袋的函数
    function deleteCjzbtm(bagno: string): Promise<boolean> {
        return axios_gh1('/deleteCjzbtm', {
            V_BAGNO: bagno
        }).then(response => {
            if (response.success && response.data === '1 rows affected' && response.msg === null) {
                return true; // 删除成功
            } else {
                return false; // 删除失败
            }
        }).catch(error => {
            console.error('Error deleting mailbag:', error);
            return false; // 删除失败
        });
    }


    function onUpdateClick() {
        let data_array = toRaw(multipleSelection.value);
        let len = data_array.length;
        if (len > 0) {
            for (let i = 0; i < len; i++) {
                //获取邮袋号
                let mailbagno = data_array[i].V_BAGNO
                updateCjzbtm(mailbagno).then((res: any) => {
                    message('绑定成功', { type: 'success' });
                }).catch((err: any) => {
                    message('绑定失败: ' + err, { type: 'error' });
                });
            }
        } else {
            message('请选择数据', { type: 'error' })
        }
    }

    function updateCjzbtm(bagno: string) {
        return axios_gh('/updatebagflag', {
            V_BAGNO: bagno
        })
    }
    

    //显示详细邮件数据
    function showDetailMailData(row) {
        let num = row.V_MAIL_NUM
        addDialog({
            title: '邮件详情(' + num + ')',
            width: 400,
            contentRenderer: () => {
                if (num > 0) {
                    let mailno = row.V_MAILNO.split(',')
                    let mailstatus = row.V_MAILSTATUS ? row.V_MAILSTATUS.split(',') : Array(mailno.length).fill('');

                    return (
                        <div>
                            {mailno.map((item, index) => {
                                return <span key={index}>{index + 1}、{item}  &nbsp; &nbsp; &nbsp;  {mailstatus[index] || ''}<br /></span>
                            })}
                            <br></br>
                        </div>
                    )
                }
            },
        });
    }

    // 重置推送状态功能
    function onResetPushStatusClick() {
        const selectedData = toRaw(multipleSelection.value);
        if (selectedData.length === 0) {
            message('请选择需要重置的数据', { type: 'error' });
            return;
        }

        addDialog({
            title: '重置推送状态',
            contentRenderer: () => <p>确定要重置 {selectedData.length} 个邮袋的推送状态吗？</p>,
            closeCallBack: ({ options, index, args }) => {
                if (args?.command === 'sure') {
                    const promises = selectedData.map(item => 
                        resetPushStatus(item.V_BAGNO)
                    );
                    
                    Promise.all(promises)
                        .then(() => {
                            message('重置推送状态成功', { type: 'success' });
                            handleSearch(tallying_select.value);
                        })
                        .catch(err => {
                            message('重置推送状态失败: ' + err, { type: 'error' });
                        });
                }
            }
        });
    }

    // 重置推送状态的API调用
    function resetPushStatus(bagno: string) {
        return axios_gh('/resetPushStatus', {
            V_BAGNO: bagno
        });
    }

    // 添加复制功能
    function copyToClipboard(text: string) {
        navigator.clipboard.writeText(text).then(() => {
            message("复制成功", { type: "success" });
        }).catch(() => {
            message("复制失败", { type: "error" });
        });
    }

    return {
        columns,
        search_columns,
        tallying_select,
        pagination,
        loadingConfig,
        dataList,
        loading,
        tableSize,
        handleChange,
        handleSearch,
        handleRest,
        onSizeChange,
        onCurrentChange,
        handleSelectionChange,
        exportExcel,
        getTallyingData,
        onPushClick,
        onStatusClick,
        onDeleteClick,
        onUpdateClick,
        onResetPushStatusClick
    };
}

// 在组件的style部分添加
const style = `
.truncate-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: pointer;
}

:deep(.el-tooltip__trigger) {
    display: inline-block;
    width: 100%;
}
`;
