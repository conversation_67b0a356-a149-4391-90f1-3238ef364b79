let flag = navigator.userAgent.match(
    /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i
);
export default {
    path: "/warehousing",
    // redirect: "/abnormalTable",
    component: () => import("@/views/Mobile/warehousing/index.vue"),
    meta: {
        icon: "icon-park-outline:warehousing",
        // showLink: flag ? true : false,
        title: "邮件入库",
        rank: 10
    }
} satisfies RouteConfigsTable;
