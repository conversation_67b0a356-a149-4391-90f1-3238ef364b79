<script setup lang="ts">
import { reactive, ref, toRaw } from "vue";
import leftLine from "@iconify-icons/ri/arrow-left-s-line";
import { type PlusColumn, type FieldValues, PlusForm } from "plus-pro-components";
import { useRouter, useRoute } from "vue-router";
import { message } from "@/utils/message";
import Cookies from "js-cookie";
import { axios_hg, axios_gh } from "@/api/user";
import { deviceDetection } from "@pureadmin/utils";
import type { FormInstance, FormRules } from 'element-plus'

const router = useRouter();
const route = useRoute();


let form_columns: PlusColumn[] = [
    {
        label: "用户ID",
        prop: "V_USER_ID",
        fieldProps: {
            disabled: true,
        }
    },
    {
        label: "旧密码",
        prop: "OLD_PSW",
        // tooltip: "显示6个字符",
        fieldProps: {
            placeholder: "请输入旧密码"
        }
    },
    {
        label: "新密码",
        prop: "NEW_PSW",
        fieldProps: {
            placeholder: "请输入新密码"
        }
    },
    {
        label: "重复新密码",
        prop: "NEW_PSW_REPEAT",
        fieldProps: {
            placeholder: "请输入重复新密码"
        }
    },
];
const ruleFormRef = ref<FormInstance>()


let modifInfo = ref<FieldValues>({
    V_USER_ID: Cookies.get("USER_ID"),
    OLD_PSW: "",
    NEW_PSW: "",
    NEW_PSW_REPEAT: ""
});

const validatePass = (rule: any, value: string, callback: any) => {
    const regex = /^(?=.*[A-Za-z])(?=.*[@$!%*?&+=.-/,|]).+$/
    if (value.length < 8) {
        // return Promise.reject("密码长度至少为8位");
    }else if (!regex.test(value)) {
        // return Promise.reject("密码至少包含字母、数字、符号");
    } else {
        callback()
    }
}

const validatePass2 = (rule: any, value: string, callback: any) => {
    if (value !== modifInfo.value.NEW_PSW) {
        // return Promise.reject("两次输入的密码不一致");
        callback(new Error("两次输入的密码不一致"))
    } else {
        callback()
    }
}

const rules = reactive<FormRules<typeof modifInfo>>({
    OLD_PSW: [
        {
            required: true,
            message: "请输入旧密码"
        }
    ],
    NEW_PSW: [
        {
            required: true,
            validator: validatePass, 
            trigger: 'blur'
        }
    ],
    NEW_PSW_REPEAT: [
        {
            required: true,
            validator: validatePass2,
            trigger: 'blur'
        }
    ]
});

const handleReset = () => {
    modifInfo = ref<FieldValues>({
        OLD_PSW: "",
        NEW_PSW: "",
        NEW_PSW_REPEAT: ""
    });
    message('重置成功', { type: 'success' })
};

const handleSubmit = (info:any) => {
   console.log(info);
   
};


</script>



<template>
    <div :class="[
        'min-w-[180px]',
        deviceDetection() ? 'max-w-[100%]' : 'max-w-[70%]'
    ]" id="modifypsw">
        <h3 class="my-8">修改密码</h3>
        <el-container>
            <el-main>
                <PlusForm v-model="modifInfo" class="w-[450px] m-auto" :columns="form_columns" :rules="rules"
                    label-position="right" @submit="handleSubmit" submit-text="修改" @reset="handleReset" reset-text="重置"
                    label-width="100">
                </PlusForm>
            </el-main>
        </el-container>
    </div>
</template>