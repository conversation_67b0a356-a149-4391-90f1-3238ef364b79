# mailNoTable模块批量查询后端实现说明

## 概述
参考tallying模块的批量查询实现，为mailNoTable模块添加批量邮件号查询功能。

## 需要实现的后端API接口

### 接口路径
`POST /cxyjxxMulti`

### 请求参数
参考现有的`/cxyjxx`接口，增加批量邮件号支持：

```java
// 原有参数保持不变
String V_MAILNO;           // 单个邮件号（保留兼容性）
List<String> V_MAILNOS;    // 批量邮件号列表（新增）
String V_OPERNAME;         // 操作人
String V_MAILRET;          // 查验结果
String V_MAILSTATUS;       // 邮件状态
String V_BAGNO;            // 邮袋号
String D_APPTIME_START;    // 入库开始时间
String D_APPTIME_END;      // 入库结束时间
String D_OPER_DATE_START;  // 出库申请开始时间
String D_OPER_DATE_END;    // 出库申请结束时间
String D_OUT_DATE_START;   // 出库开始时间
String D_OUT_DATE_END;     // 出库结束时间
```

### SQL实现
参考tallying模块的批量查询SQL，修改邮件号查询的SQL。

**完整的SQL样式请参考同目录下的 `邮件号批量查询SQL.sql` 文件。**

关键修改点：
1. 添加批量查询条件：
```xml
<if test="V_MAILNOS != null and !V_MAILNOS.isEmpty()">
    AND m.V_MAILNO IN
    <foreach item="V_MAILNO" collection="V_MAILNOS" open="(" separator="," close=")">
        #{V_MAILNO}
    </foreach>
</if>
```

2. 保持原有的单个邮件号查询兼容性：
```xml
<if test="V_MAILNO != null and V_MAILNO != ''">
    AND m.V_MAILNO like '%' || #{V_MAILNO} || '%'
</if>
```

3. 其他查询条件保持不变

### 实现步骤

1. **Controller层**
   - 复制现有的`/cxyjxx`接口实现
   - 创建新的`/cxyjxxMulti`接口
   - 添加对`V_MAILNOS`参数的处理

2. **Service层**
   - 复制现有的邮件查询服务方法
   - 创建新的批量查询方法
   - 处理批量邮件号参数

3. **Mapper层**
   - 在MyBatis XML文件中添加新的SQL查询
   - 使用`<foreach>`标签处理批量邮件号查询

### 返回数据格式
与现有的`/cxyjxx`接口保持一致，返回邮件信息列表。

## 前端已完成的修改

1. ✅ 创建了`MultiMailnoInput`组件，支持批量输入邮件号
2. ✅ 修改了mailNoTable的搜索配置，使用新的批量输入组件
3. ✅ 实现了批量查询逻辑，自动判断单个/批量查询
4. ✅ 添加了`getMailNoDataMulti`函数调用新的API接口

## 测试建议

1. 测试单个邮件号查询（应使用原有接口）
2. 测试多个邮件号批量查询（应使用新接口）
3. 测试批量查询的各种过滤条件组合
4. 测试大量邮件号的性能表现

## 注意事项

- 保持与现有`/cxyjxx`接口的兼容性
- 批量查询时注意SQL性能优化
- 考虑添加批量查询的数量限制（建议不超过100个）
- 错误处理和日志记录
