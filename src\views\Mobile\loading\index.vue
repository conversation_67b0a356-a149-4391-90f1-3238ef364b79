<script setup lang="ts">
import "plus-pro-components/es/components/search/style/css";
import { reactive, toRaw, ref, onMounted, getCurrentInstance } from "vue";
import { useColumns } from "./index";

const {
    searchValue,
    searchRef,
    auditNoResult,
    cell,
    endBind,
    showButton,
    onSubmit,
    outbandAuditEndBindReport,
    onSearch

} = useColumns();

onMounted(() => {
    searchRef.value && searchRef.value.focus();
})

// getMine().then(res => {
//     console.log(res.data);

// });

</script>

<template>
    <div class="Loading">
        <header>
            <van-search v-model="searchValue" shape="round" background="#1989fa" placeholder="请扫描邮袋号" @search="onSearch"
                ref="searchRef" />
        </header>
        <main>
            <van-cell-group>
                <van-field v-model="auditNoResult.V_AUDIT_NO" label="序列号" readonly />
                <van-field v-model="auditNoResult.V_CHECK" label="审核结果" readonly />
                <van-field v-model="auditNoResult.V_PASSDESC" label="反馈信息" readonly type="textarea" />
            </van-cell-group>
            <van-divider :style="{ color: '#1989fa', borderColor: '#1989fa', padding: '0 16px' }">
                同序列号的邮袋({{ cell.length }})
            </van-divider>
            <div class="tips">
                <van-icon name="clear" class="clear" color="#ee0a24" />命中
                <van-icon name="checked" class="checked" color="#07f013" />未命中
                <van-icon name="warning" class="warning" color="#f09e07" />绑定失败
                <van-icon name="question" class="question" color="#1989fa" />未绑定
            </div>
            <div class="all">
                <van-cell v-for="(item, index) in cell" :key="index" :label="item.V_PASSDESC" :title="item.V_BAGNO">
                    <template #right-icon>
                        <van-icon v-if="item.V_CHECK == '0'" name="checked" class="checked" color="#07f013" />
                        <van-icon v-else-if="item.V_CHECK == '1'" name="clear" class="clear" color="#ee0a24" />
                        <van-icon v-else-if="item.V_CHECK == '-1'" name="warning" class="warning" color="#f09e07" />
                        <van-icon v-else name="question" class="question" color="#1989fa" />
                    </template>
                </van-cell>
            </div>
            <div style="margin: 16px;">
                <!-- <van-button type="primary" block @click="onSubmit" round v-if="endBind">一键绑定</van-button>
                <van-button type="primary" block @click="outbandAuditEndBindReport" round v-else>结束绑定</van-button> -->
                <van-button type="primary" block @click="onSubmit" round>一键绑定</van-button>
                <br/>
                <van-button type="primary" block @click="outbandAuditEndBindReport" round>结束绑定</van-button>
            </div>
        </main>
    </div>
</template>

<style scoped>
main {
    padding-bottom: 50px;
}

.all {
    height: 50vh;
    overflow: scroll;
    margin: 0 5px;
    border: 2px dotted #1989fa;
    margin-bottom: 2vh;
}
</style>